import pandas as pd
import json
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# Chargement du fichier JSON
with open('../Json/crm_data.json', 'r', encoding='utf-8') as file:
    json_data = json.load(file)

print(f"Status: {json_data['status']}")
print(f"Nombre de programmes: {len(json_data['data'])}")

# Extraction des données principales
programmes_data = []

for programme in json_data['data']:
    programme_info = {
        'code': programme['code'],
        'programme': programme['libelle'],  # Renommer libelle en programme
        'localisation': programme['localisation'],
        'img': programme['img'],
        'nb_logs': len(programme['log']) if 'log' in programme else 0
    }
    
    # Ajouter les statistiques si elles existent
    if 'statistiques' in programme:
        stats = programme['statistiques']
        programme_info.update({
            'ventes': stats.get('ventes', 0),
            'chiffre_affaire': stats.get('chiffre_affaire', 0),
            'recouvrement': stats.get('recouvrement', 0),
            'reste_a_recouvrer': stats.get('reste_a_recouvrer', 0),
            'montant_vh': stats.get('montant_vh', 0)
        })
    
    programmes_data.append(programme_info)

# Création du DataFrame parent
df_parent = pd.DataFrame(programmes_data)
print(f"DataFrame parent créé avec {len(df_parent)} programmes")
df_parent.head()

# Nettoyage des données
df_parent = df_parent.fillna(0)

# Conversion des montants en format numérique
numeric_columns = ['ventes', 'chiffre_affaire', 'recouvrement', 'reste_a_recouvrer', 'montant_vh']
for col in numeric_columns:
    if col in df_parent.columns:
        df_parent[col] = pd.to_numeric(df_parent[col], errors='coerce').fillna(0)

print("Nettoyage des données terminé")
df_parent.info()

def extract_logs_data():
    """Extraction des données de logs pour chaque programme"""
    logs_data = []
    
    for programme in json_data['data']:
        programme_name = programme['libelle']
        
        if 'log' in programme:
            for log in programme['log']:
                log_info = {
                    'programme': programme_name,
                    'type_id': log.get('typeclient_id'),
                    'action': log.get('action'),
                    'commentaire': log.get('commentaire'),
                    'created_at': log.get('created_at')
                }
                logs_data.append(log_info)
    
    return pd.DataFrame(logs_data)

def extract_users_data():
    """Extraction des données utilisateurs pour chaque programme"""
    users_data = []
    
    for programme in json_data['data']:
        programme_name = programme['libelle']
        
        if 'log' in programme:
            for log in programme['log']:
                if 'user' in log:
                    user = log['user']
                    user_info = {
                        'programme': programme_name,
                        'firstname': user.get('firstname'),
                        'lastname': user.get('lastname'),
                        'fonction': user.get('fonction'),
                        'phone': user.get('phone'),
                        'email': user.get('email')
                    }
                    users_data.append(user_info)
    
    # Supprimer les doublons
    df_users = pd.DataFrame(users_data)
    if not df_users.empty:
        df_users = df_users.drop_duplicates()
    
    return df_users

def extract_stats_data():
    """Extraction des statistiques pour chaque programme"""
    stats_data = []
    
    for programme in json_data['data']:
        programme_name = programme['libelle']
        
        if 'statistiques' in programme:
            stats = programme['statistiques']
            stats_info = {
                'programme': programme_name,
                'ventes': stats.get('ventes', 0),
                'chiffre_affaire': stats.get('chiffre_affaire', 0),
                'recouvrement': stats.get('recouvrement', 0),
                'reste_a_recouvrer': stats.get('reste_a_recouvrer', 0),
                'montant_vh': stats.get('montant_vh', 0)
            }
            stats_data.append(stats_info)
    
    return pd.DataFrame(stats_data)

print("Fonctions d'éclatement définies")

# Création des DataFrames éclatés
df_programmes = df_parent[['code', 'programme', 'localisation']].copy()
df_logs = extract_logs_data()
df_users = extract_users_data()
df_stats = extract_stats_data()

print(f"DataFrame programmes: {len(df_programmes)} lignes")
print(f"DataFrame logs: {len(df_logs)} lignes")
print(f"DataFrame users: {len(df_users)} lignes")
print(f"DataFrame stats: {len(df_stats)} lignes")

# Sauvegarde des DataFrames en CSV
df_parent.to_csv('crm_data.csv', index=False, encoding='utf-8')
df_programmes.to_csv('crm_programmes.csv', index=False, encoding='utf-8')
df_logs.to_csv('crm_logs.csv', index=False, encoding='utf-8')
df_users.to_csv('crm_users.csv', index=False, encoding='utf-8')
df_stats.to_csv('crm_stats.csv', index=False, encoding='utf-8')

print("Fichiers CSV sauvegardés:")
print("- crm_data.csv (données parent)")
print("- crm_programmes.csv (liste des programmes)")
print("- crm_logs.csv (logs par programme)")
print("- crm_users.csv (utilisateurs par programme)")
print("- crm_stats.csv (statistiques par programme)")

# Rechargement des données depuis les fichiers CSV
df_parent_reload = pd.read_csv('crm_data.csv')
df_programmes_reload = pd.read_csv('crm_programmes.csv')
df_logs_reload = pd.read_csv('crm_logs.csv')
df_users_reload = pd.read_csv('crm_users.csv')
df_stats_reload = pd.read_csv('crm_stats.csv')

print("Données rechargées depuis les fichiers CSV")

def analyze_programmes():
    """Analyse des programmes"""
    print("=== ANALYSE DES PROGRAMMES ===")
    print(f"Nombre total de programmes: {len(df_programmes_reload)}")
    print(f"Programmes par localisation:")
    print(df_programmes_reload['localisation'].value_counts())
    return df_programmes_reload

def analyze_logs():
    """Analyse des logs"""
    print("\n=== ANALYSE DES LOGS ===")
    print(f"Nombre total de logs: {len(df_logs_reload)}")
    print(f"Actions les plus fréquentes:")
    print(df_logs_reload['action'].value_counts().head(10))
    print(f"\nLogs par programme:")
    print(df_logs_reload['programme'].value_counts())
    return df_logs_reload

def analyze_users():
    """Analyse des utilisateurs"""
    print("\n=== ANALYSE DES UTILISATEURS ===")
    print(f"Nombre total d'utilisateurs uniques: {len(df_users_reload)}")
    print(f"Utilisateurs par fonction:")
    print(df_users_reload['fonction'].value_counts())
    print(f"\nUtilisateurs par programme:")
    print(df_users_reload['programme'].value_counts())
    return df_users_reload

def analyze_stats():
    """Analyse des statistiques"""
    print("\n=== ANALYSE DES STATISTIQUES ===")
    print(f"Nombre de programmes avec statistiques: {len(df_stats_reload)}")
    print(f"Total des ventes: {df_stats_reload['ventes'].sum()}")
    print(f"Chiffre d'affaires total: {df_stats_reload['chiffre_affaire'].sum():,.0f}")
    print(f"Recouvrement total: {df_stats_reload['recouvrement'].sum():,.0f}")
    print(f"Reste à recouvrer total: {df_stats_reload['reste_a_recouvrer'].sum():,.0f}")
    return df_stats_reload

print("Fonctions d'analyse définies")

print("=== DATAFRAME PARENT ===")
print(f"Dimensions: {df_parent_reload.shape}")
print("\nAperçu des données:")
display(df_parent_reload)

df_programmes_analyzed = analyze_programmes()
print("\nDataFrame des programmes:")
display(df_programmes_analyzed)

df_logs_analyzed = analyze_logs()
print("\nDataFrame des logs:")
display(df_logs_analyzed)

df_users_analyzed = analyze_users()
print("\nDataFrame des utilisateurs:")
display(df_users_analyzed)

df_stats_analyzed = analyze_stats()
print("\nDataFrame des statistiques:")
display(df_stats_analyzed)

if not df_stats_reload.empty:
    fig_ventes = px.bar(
        df_stats_reload.sort_values('ventes', ascending=False),
        x='programme',
        y='ventes',
        title='Nombre de ventes par programme',
        labels={'ventes': 'Nombre de ventes', 'programme': 'Programme'}
    )
    fig_ventes.update_layout(xaxis_tickangle=-45)
    fig_ventes.show()
else:
    print("Aucune donnée de statistiques disponible")

if not df_stats_reload.empty:
    fig_ca = px.bar(
        df_stats_reload.sort_values('chiffre_affaire', ascending=False),
        x='programme',
        y='chiffre_affaire',
        title='Chiffre d\'affaires par programme',
        labels={'chiffre_affaire': 'Chiffre d\'affaires (FCFA)', 'programme': 'Programme'}
    )
    fig_ca.update_layout(xaxis_tickangle=-45)
    fig_ca.show()
else:
    print("Aucune donnée de statistiques disponible")

if not df_logs_reload.empty:
    action_counts = df_logs_reload['action'].value_counts().head(10)
    fig_actions = px.pie(
        values=action_counts.values,
        names=action_counts.index,
        title='Répartition des 10 actions les plus fréquentes'
    )
    fig_actions.show()
else:
    print("Aucune donnée de logs disponible")

if not df_logs_reload.empty:
    logs_par_programme = df_logs_reload['programme'].value_counts()
    fig_activite = px.bar(
        x=logs_par_programme.index,
        y=logs_par_programme.values,
        title='Activité par programme (nombre de logs)',
        labels={'x': 'Programme', 'y': 'Nombre de logs'}
    )
    fig_activite.update_layout(xaxis_tickangle=-45)
    fig_activite.show()
else:
    print("Aucune donnée de logs disponible")

if not df_users_reload.empty:
    fonction_counts = df_users_reload['fonction'].value_counts()
    fig_fonctions = px.pie(
        values=fonction_counts.values,
        names=fonction_counts.index,
        title='Répartition des utilisateurs par fonction'
    )
    fig_fonctions.show()
else:
    print("Aucune donnée d'utilisateurs disponible")

print("=== RÉSUMÉ DES DATAFRAMES CRÉÉS ===")
print(f"\n1. DataFrame Parent: {df_parent_reload.shape[0]} lignes, {df_parent_reload.shape[1]} colonnes")
print(f"   Colonnes: {list(df_parent_reload.columns)}")

print(f"\n2. DataFrame Programmes: {df_programmes_reload.shape[0]} lignes, {df_programmes_reload.shape[1]} colonnes")
print(f"   Colonnes: {list(df_programmes_reload.columns)}")

print(f"\n3. DataFrame Logs: {df_logs_reload.shape[0]} lignes, {df_logs_reload.shape[1]} colonnes")
print(f"   Colonnes: {list(df_logs_reload.columns)}")

print(f"\n4. DataFrame Users: {df_users_reload.shape[0]} lignes, {df_users_reload.shape[1]} colonnes")
print(f"   Colonnes: {list(df_users_reload.columns)}")

print(f"\n5. DataFrame Stats: {df_stats_reload.shape[0]} lignes, {df_stats_reload.shape[1]} colonnes")
print(f"   Colonnes: {list(df_stats_reload.columns)}")