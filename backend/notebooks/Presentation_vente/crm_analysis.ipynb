{"cells": [{"cell_type": "markdown", "id": "22c03095", "metadata": {}, "source": ["# Analyse des données CRM\n", "## Chargement et analyse des données de présentation/vente"]}, {"cell_type": "markdown", "id": "6edd6534", "metadata": {}, "source": ["### 1. Imports des bibliothèques"]}, {"cell_type": "code", "execution_count": 13, "id": "2dbcafe8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import ast"]}, {"cell_type": "markdown", "id": "6ebbcc8e", "metadata": {}, "source": ["### 2. Chargement du fichier CSV"]}, {"cell_type": "code", "execution_count": 14, "id": "6a8875bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Données chargées: 1 programmes\n", "Colonnes disponibles: ['status', 'data']\n"]}], "source": ["# Chargement du fichier CSV existant\n", "df_parent = pd.read_csv('crm_data.csv')\n", "print(f\"Données chargées: {len(df_parent)} programmes\")\n", "print(f\"Colonnes disponibles: {list(df_parent.columns)}\")"]}, {"cell_type": "markdown", "id": "79ed24c9", "metadata": {}, "source": ["### 3. Data Cleaning - Suppression et renommage des colonnes"]}, {"cell_type": "code", "execution_count": 15, "id": "6f0a2c90", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATA CLEANING ===\n", "Colonnes avant nettoyage: ['status', 'data']\n", "\n", "Colonnes après nettoyage: ['status', 'data']\n"]}], "source": ["print(\"=== DATA CLEANING ===\")\n", "print(f\"Colonnes avant nettoyage: {list(df_parent.columns)}\")\n", "\n", "# Suppression des colonnes id et slug si elles existent\n", "columns_to_drop = ['id', 'slug']\n", "for col in columns_to_drop:\n", "    if col in df_parent.columns:\n", "        df_parent = df_parent.drop(columns=[col])\n", "        print(f\"Colonne '{col}' supprimée\")\n", "\n", "# Renommage de libelle en programme si la colonne existe\n", "if 'libelle' in df_parent.columns:\n", "    df_parent = df_parent.rename(columns={'libelle': 'programme'})\n", "    print(\"Colonne 'libelle' renommée en 'programme'\")\n", "\n", "print(f\"\\nColonnes après nettoyage: {list(df_parent.columns)}\")"]}, {"cell_type": "markdown", "id": "c9b76942", "metadata": {}, "source": ["### 4. <PERSON><PERSON><PERSON><PERSON> du DataFrame parent"]}, {"cell_type": "code", "execution_count": 16, "id": "b66e0877", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DATAFRAME PARENT ===\n", "Dimensions: (1, 2)\n", "\n", "Aperçu des données:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>status</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>success</td>\n", "      <td>[{'id': 1, 'code': 'KAY0075', 'slug': 'borefle...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    status                                               data\n", "0  success  [{'id': 1, 'code': 'KAY0075', 'slug': 'borefle..."]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"=== DATAFRAME PARENT ===\")\n", "print(f\"Dimensions: {df_parent.shape}\")\n", "print(\"\\nAperçu des données:\")\n", "display(df_parent)"]}, {"cell_type": "markdown", "id": "10ce3c47", "metadata": {}, "source": ["### 5. Exploration de la structure des données"]}, {"cell_type": "code", "execution_count": 17, "id": "272b3ce1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== EXPLORATION DE LA STRUCTURE ===\n", "\n", "Informations sur le DataFrame:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1 entries, 0 to 0\n", "Data columns (total 2 columns):\n", " #   Column  Non-Null Count  Dtype \n", "---  ------  --------------  ----- \n", " 0   status  1 non-null      object\n", " 1   data    1 non-null      object\n", "dtypes: object(2)\n", "memory usage: 148.0+ bytes\n", "\n", "Statistiques descriptives:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>status</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>success</td>\n", "      <td>[{'id': 1, 'code': 'KAY0075', 'slug': 'borefle...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         status                                               data\n", "count         1                                                  1\n", "unique        1                                                  1\n", "top     success  [{'id': 1, 'code': 'KAY0075', 'slug': 'borefle...\n", "freq          1                                                  1"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Valeurs manquantes:\n", "status    0\n", "data      0\n", "dtype: int64\n"]}], "source": ["print(\"=== EXPLORATION DE LA STRUCTURE ===\")\n", "print(\"\\nInformations sur le DataFrame:\")\n", "df_parent.info()\n", "\n", "print(\"\\nStatistiques descriptives:\")\n", "display(df_parent.describe())\n", "\n", "print(\"\\nValeurs manquantes:\")\n", "print(df_parent.isnull().sum())"]}, {"cell_type": "markdown", "id": "7de639c5", "metadata": {}, "source": ["### 6. Analyse des données par programme"]}, {"cell_type": "code", "execution_count": 18, "id": "05571e46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ANALYSE PAR PROGRAMME ===\n", "\n", "Programmes disponibles:\n"]}, {"ename": "KeyError", "evalue": "'programme'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/indexes/base.py:3805\u001b[39m, in \u001b[36mIndex.get_loc\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   3804\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3805\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._engine.get_loc(casted_key)\n\u001b[32m   3806\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32mindex.pyx:167\u001b[39m, in \u001b[36mpandas._libs.index.IndexEngine.get_loc\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mindex.pyx:196\u001b[39m, in \u001b[36mpandas._libs.index.IndexEngine.get_loc\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/hashtable_class_helper.pxi:7081\u001b[39m, in \u001b[36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/hashtable_class_helper.pxi:7089\u001b[39m, in \u001b[36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 'programme'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[18]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mProgrammes disponibles:\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m idx, row \u001b[38;5;129;01min\u001b[39;00m df_parent.iterrows():\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m- \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[33m'\u001b[39m\u001b[33mprogramme\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m (\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[33m'\u001b[39m\u001b[33mcode\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m) - \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[33m'\u001b[39m\u001b[33mlocalisation\u001b[39m\u001b[33m'\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/series.py:1121\u001b[39m, in \u001b[36mSeries.__getitem__\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   1118\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._values[key]\n\u001b[32m   1120\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m key_is_scalar:\n\u001b[32m-> \u001b[39m\u001b[32m1121\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._get_value(key)\n\u001b[32m   1123\u001b[39m \u001b[38;5;66;03m# Convert generator to list before going through hashable part\u001b[39;00m\n\u001b[32m   1124\u001b[39m \u001b[38;5;66;03m# (We will iterate through the generator there to check for slices)\u001b[39;00m\n\u001b[32m   1125\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m is_iterator(key):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/series.py:1237\u001b[39m, in \u001b[36mSeries._get_value\u001b[39m\u001b[34m(self, label, takeable)\u001b[39m\n\u001b[32m   1234\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._values[label]\n\u001b[32m   1236\u001b[39m \u001b[38;5;66;03m# Similar to Index.get_value, but we do not fall back to positional\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1237\u001b[39m loc = \u001b[38;5;28mself\u001b[39m.index.get_loc(label)\n\u001b[32m   1239\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m is_integer(loc):\n\u001b[32m   1240\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._values[loc]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/KAH/lib/python3.13/site-packages/pandas/core/indexes/base.py:3812\u001b[39m, in \u001b[36mIndex.get_loc\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   3807\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[32m   3808\u001b[39m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc.Iterable)\n\u001b[32m   3809\u001b[39m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[32m   3810\u001b[39m     ):\n\u001b[32m   3811\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[32m-> \u001b[39m\u001b[32m3812\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON>rro<PERSON>\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n\u001b[32m   3813\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[32m   3814\u001b[39m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[32m   3815\u001b[39m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[32m   3816\u001b[39m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[32m   3817\u001b[39m     \u001b[38;5;28mself\u001b[39m._check_indexing_error(key)\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 'programme'"]}], "source": ["print(\"=== ANALYSE PAR PROGRAMME ===\")\n", "print(\"\\nProgrammes disponibles:\")\n", "for idx, row in df_parent.iterrows():\n", "    print(f\"- {row['programme']} ({row['code']}) - {row['localisation']}\")"]}, {"cell_type": "markdown", "id": "ff06fbb6", "metadata": {}, "source": ["### 7. Chargement du JSON pour l'éclatement des données"]}, {"cell_type": "code", "execution_count": null, "id": "de43a3d5", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Chargement du fichier JSON pour accéder aux données détaillées\n", "with open('../Json/crm_data.json', 'r', encoding='utf-8') as file:\n", "    json_data = json.load(file)\n", "\n", "print(f\"JSON chargé - Status: {json_data['status']}\")\n", "print(f\"Nombre de programmes dans le JSON: {len(json_data['data'])}\")"]}, {"cell_type": "markdown", "id": "d6c26932", "metadata": {}, "source": ["### 8. Fonctions d'éclatement des données"]}, {"cell_type": "code", "execution_count": null, "id": "0e2264d3", "metadata": {}, "outputs": [], "source": ["def extract_logs_data():\n", "    \"\"\"Extraction des données de logs pour chaque programme\"\"\"\n", "    logs_data = []\n", "    \n", "    for programme in json_data['data']:\n", "        programme_name = programme['libelle']\n", "        \n", "        if 'log' in programme:\n", "            for log in programme['log']:\n", "                log_info = {\n", "                    'programme': programme_name,\n", "                    'type_id': log.get('typeclient_id'),\n", "                    'action': log.get('action'),\n", "                    'commentaire': log.get('commentaire')\n", "                }\n", "                logs_data.append(log_info)\n", "    \n", "    return pd.DataFrame(logs_data)\n", "\n", "def extract_users_data():\n", "    \"\"\"Extraction des données utilisateurs pour chaque programme\"\"\"\n", "    users_data = []\n", "    \n", "    for programme in json_data['data']:\n", "        programme_name = programme['libelle']\n", "        \n", "        if 'log' in programme:\n", "            for log in programme['log']:\n", "                if 'user' in log:\n", "                    user = log['user']\n", "                    user_info = {\n", "                        'programme': programme_name,\n", "                        'firstname': user.get('firstname'),\n", "                        'lastname': user.get('lastname'),\n", "                        'fonction': user.get('fonction'),\n", "                        'phone': user.get('phone'),\n", "                        'email': user.get('email')\n", "                    }\n", "                    users_data.append(user_info)\n", "    \n", "    # Supprimer les doublons\n", "    df_users = pd.DataFrame(users_data)\n", "    if not df_users.empty:\n", "        df_users = df_users.drop_duplicates()\n", "    \n", "    return df_users\n", "\n", "def extract_stats_data():\n", "    \"\"\"Extraction des statistiques pour chaque programme\"\"\"\n", "    stats_data = []\n", "    \n", "    for programme in json_data['data']:\n", "        programme_name = programme['libelle']\n", "        \n", "        if 'statistiques' in programme:\n", "            stats = programme['statistiques']\n", "            stats_info = {\n", "                'programme': programme_name,\n", "                'ventes': stats.get('ventes', 0),\n", "                'chiffre_affaire': stats.get('chiffre_affaire', 0),\n", "                'recouvrement': stats.get('recouvrement', 0),\n", "                'reste_a_recouvrer': stats.get('reste_a_recouvrer', 0),\n", "                'montant_vh': stats.get('montant_vh', 0)\n", "            }\n", "            stats_data.append(stats_info)\n", "    \n", "    return pd.DataFrame(stats_data)\n", "\n", "print(\"Fonctions d'éclatement définies\")"]}, {"cell_type": "markdown", "id": "6c828a54", "metadata": {}, "source": ["### 9. Création des DataFrames éclatés"]}, {"cell_type": "code", "execution_count": null, "id": "8fbd5ca4", "metadata": {}, "outputs": [], "source": ["# Création des DataFrames éclatés\n", "df_programmes = df_parent[['code', 'programme', 'localisation']].copy()\n", "df_logs = extract_logs_data()\n", "df_users = extract_users_data()\n", "df_stats = extract_stats_data()\n", "\n", "print(f\"DataFrame programmes: {len(df_programmes)} lignes\")\n", "print(f\"DataFrame logs: {len(df_logs)} lignes\")\n", "print(f\"DataFrame users: {len(df_users)} lignes\")\n", "print(f\"DataFrame stats: {len(df_stats)} lignes\")"]}, {"cell_type": "markdown", "id": "78f3fb6b", "metadata": {}, "source": ["### 10. Affichage des DataFrames"]}, {"cell_type": "markdown", "id": "7b2ce777", "metadata": {}, "source": ["#### 9.1 DataFrame des Programmes"]}, {"cell_type": "code", "execution_count": null, "id": "bf81ccf6", "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME DES PROGRAMMES ===\")\n", "print(f\"Dimensions: {df_programmes.shape}\")\n", "display(df_programmes)"]}, {"cell_type": "markdown", "id": "04bfb542", "metadata": {}, "source": ["#### 9.2 DataFrame des Logs par Programme"]}, {"cell_type": "code", "execution_count": null, "id": "e0fc014f", "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME DES LOGS ===\")\n", "print(f\"Dimensions: {df_logs.shape}\")\n", "print(f\"\\nActions les plus fréquentes:\")\n", "print(df_logs['action'].value_counts().head(10))\n", "print(f\"\\nLogs par programme:\")\n", "print(df_logs['programme'].value_counts())\n", "print(\"\\nAperçu des logs:\")\n", "display(df_logs.head(20))"]}, {"cell_type": "markdown", "id": "3dc9c4d5", "metadata": {}, "source": ["#### 9.3 DataFrame des Utilisateurs par Programme"]}, {"cell_type": "code", "execution_count": null, "id": "4764a4fd", "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME DES UTILISATEURS ===\")\n", "print(f\"Dimensions: {df_users.shape}\")\n", "print(f\"\\nUtilisateurs par fonction:\")\n", "print(df_users['fonction'].value_counts())\n", "print(f\"\\nUtilisateurs par programme:\")\n", "print(df_users['programme'].value_counts())\n", "print(\"\\nAperçu des utilisateurs:\")\n", "display(df_users)"]}, {"cell_type": "markdown", "id": "557308df", "metadata": {}, "source": ["#### 9.4 DataFrame des Statistiques par Programme"]}, {"cell_type": "code", "execution_count": null, "id": "a3b4a524", "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME DES STATISTIQUES ===\")\n", "print(f\"Dimensions: {df_stats.shape}\")\n", "print(f\"\\nTotal des ventes: {df_stats['ventes'].sum()}\")\n", "print(f\"Chiffre d'affaires total: {df_stats['chiffre_affaire'].sum():,.0f} FCFA\")\n", "print(f\"Recouvrement total: {df_stats['recouvrement'].sum():,.0f} FCFA\")\n", "print(f\"Reste à recouvrer total: {df_stats['reste_a_recouvrer'].sum():,.0f} FCFA\")\n", "print(\"\\nStatistiques par programme:\")\n", "display(df_stats)"]}, {"cell_type": "markdown", "id": "e44da988", "metadata": {}, "source": ["### 11. <PERSON><PERSON><PERSON> par programme"]}, {"cell_type": "code", "execution_count": null, "id": "132f5e6e", "metadata": {}, "outputs": [], "source": ["print(\"=== ANALYSE DÉTAILLÉE PAR PROGRAMME ===\")\n", "\n", "for programme_name in df_programmes['programme'].unique():\n", "    print(f\"\\n{'='*50}\")\n", "    print(f\"PROGRAMME: {programme_name}\")\n", "    print(f\"{'='*50}\")\n", "    \n", "    # Logs pour ce programme\n", "    logs_prog = df_logs[df_logs['programme'] == programme_name]\n", "    print(f\"\\nNombre de logs: {len(logs_prog)}\")\n", "    if not logs_prog.empty:\n", "        print(\"Actions principales:\")\n", "        print(logs_prog['action'].value_counts().head(5))\n", "        print(\"\\nDerniers logs:\")\n", "        display(logs_prog.tail(10))\n", "    \n", "    # Utilisateurs pour ce programme\n", "    users_prog = df_users[df_users['programme'] == programme_name]\n", "    print(f\"\\nNombre d'utilisateurs: {len(users_prog)}\")\n", "    if not users_prog.empty:\n", "        print(\"Utilisateurs:\")\n", "        display(users_prog)\n", "    \n", "    # Statistiques pour ce programme\n", "    stats_prog = df_stats[df_stats['programme'] == programme_name]\n", "    if not stats_prog.empty:\n", "        print(\"\\nStatistiques:\")\n", "        display(stats_prog)"]}, {"cell_type": "markdown", "id": "f38e54a2", "metadata": {}, "source": ["### 12. R<PERSON>um<PERSON> des DataFrames créés"]}, {"cell_type": "code", "execution_count": null, "id": "31661207", "metadata": {}, "outputs": [], "source": ["print(\"=== R<PERSON><PERSON><PERSON><PERSON> DES DATAFRAMES CRÉÉS ===\")\n", "print(f\"\\n1. DataFrame Parent: {df_parent.shape[0]} lignes, {df_parent.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_parent.columns)}\")\n", "\n", "print(f\"\\n2. DataFrame Programmes: {df_programmes.shape[0]} lignes, {df_programmes.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_programmes.columns)}\")\n", "\n", "print(f\"\\n3. DataFrame Logs: {df_logs.shape[0]} lignes, {df_logs.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_logs.columns)}\")\n", "\n", "print(f\"\\n4. DataFrame Users: {df_users.shape[0]} lignes, {df_users.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_users.columns)}\")\n", "\n", "print(f\"\\n5. DataFrame Stats: {df_stats.shape[0]} lignes, {df_stats.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_stats.columns)}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}