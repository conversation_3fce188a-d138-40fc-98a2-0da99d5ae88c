{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Analyse des données CRM\n", "## Chargement et analyse des données de présentation/vente"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Imports des bibliothèques"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import ast"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2. Chargement du fichier CSV"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Chargement du fichier CSV existant\n", "df_parent = pd.read_csv('crm_data.csv')\n", "print(f\"Données chargées: {len(df_parent)} programmes\")\n", "print(f\"Colonnes disponibles: {list(df_parent.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3. <PERSON><PERSON><PERSON><PERSON> du DataFrame parent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME PARENT ===\")\n", "print(f\"Dimensions: {df_parent.shape}\")\n", "print(\"\\nAperçu des données:\")\n", "display(df_parent)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4. Exploration de la structure des données"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== EXPLORATION DE LA STRUCTURE ===\")\n", "print(\"\\nInformations sur le DataFrame:\")\n", "df_parent.info()\n", "\n", "print(\"\\nStatistiques descriptives:\")\n", "display(df_parent.describe())\n", "\n", "print(\"\\nValeurs manquantes:\")\n", "print(df_parent.isnull().sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5. Analyse des données par programme"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== ANALYSE PAR PROGRAMME ===\")\n", "print(\"\\nProgrammes disponibles:\")\n", "for idx, row in df_parent.iterrows():\n", "    print(f\"- {row['programme']} ({row['code']}) - {row['localisation']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6. Chargement du JSON pour l'éclatement des données"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Chargement du fichier JSON pour accéder aux données détaillées\n", "with open('../Json/crm_data.json', 'r', encoding='utf-8') as file:\n", "    json_data = json.load(file)\n", "\n", "print(f\"JSON chargé - Status: {json_data['status']}\")\n", "print(f\"Nombre de programmes dans le JSON: {len(json_data['data'])}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 7. Fonctions d'éclatement des données"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def extract_logs_data():\n", "    \"\"\"Extraction des données de logs pour chaque programme\"\"\"\n", "    logs_data = []\n", "    \n", "    for programme in json_data['data']:\n", "        programme_name = programme['libelle']\n", "        \n", "        if 'log' in programme:\n", "            for log in programme['log']:\n", "                log_info = {\n", "                    'programme': programme_name,\n", "                    'type_id': log.get('typeclient_id'),\n", "                    'action': log.get('action'),\n", "                    'commentaire': log.get('commentaire')\n", "                }\n", "                logs_data.append(log_info)\n", "    \n", "    return pd.DataFrame(logs_data)\n", "\n", "def extract_users_data():\n", "    \"\"\"Extraction des données utilisateurs pour chaque programme\"\"\"\n", "    users_data = []\n", "    \n", "    for programme in json_data['data']:\n", "        programme_name = programme['libelle']\n", "        \n", "        if 'log' in programme:\n", "            for log in programme['log']:\n", "                if 'user' in log:\n", "                    user = log['user']\n", "                    user_info = {\n", "                        'programme': programme_name,\n", "                        'firstname': user.get('firstname'),\n", "                        'lastname': user.get('lastname'),\n", "                        'fonction': user.get('fonction'),\n", "                        'phone': user.get('phone'),\n", "                        'email': user.get('email')\n", "                    }\n", "                    users_data.append(user_info)\n", "    \n", "    # Supprimer les doublons\n", "    df_users = pd.DataFrame(users_data)\n", "    if not df_users.empty:\n", "        df_users = df_users.drop_duplicates()\n", "    \n", "    return df_users\n", "\n", "def extract_stats_data():\n", "    \"\"\"Extraction des statistiques pour chaque programme\"\"\"\n", "    stats_data = []\n", "    \n", "    for programme in json_data['data']:\n", "        programme_name = programme['libelle']\n", "        \n", "        if 'statistiques' in programme:\n", "            stats = programme['statistiques']\n", "            stats_info = {\n", "                'programme': programme_name,\n", "                'ventes': stats.get('ventes', 0),\n", "                'chiffre_affaire': stats.get('chiffre_affaire', 0),\n", "                'recouvrement': stats.get('recouvrement', 0),\n", "                'reste_a_recouvrer': stats.get('reste_a_recouvrer', 0),\n", "                'montant_vh': stats.get('montant_vh', 0)\n", "            }\n", "            stats_data.append(stats_info)\n", "    \n", "    return pd.DataFrame(stats_data)\n", "\n", "print(\"Fonctions d'éclatement définies\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8. Création des DataFrames éclatés"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Création des DataFrames éclatés\n", "df_programmes = df_parent[['code', 'programme', 'localisation']].copy()\n", "df_logs = extract_logs_data()\n", "df_users = extract_users_data()\n", "df_stats = extract_stats_data()\n", "\n", "print(f\"DataFrame programmes: {len(df_programmes)} lignes\")\n", "print(f\"DataFrame logs: {len(df_logs)} lignes\")\n", "print(f\"DataFrame users: {len(df_users)} lignes\")\n", "print(f\"DataFrame stats: {len(df_stats)} lignes\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 9. Affichage des DataFrames"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 9.1 DataFrame des Programmes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME DES PROGRAMMES ===\")\n", "print(f\"Dimensions: {df_programmes.shape}\")\n", "display(df_programmes)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 9.2 DataFrame des Logs par Programme"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME DES LOGS ===\")\n", "print(f\"Dimensions: {df_logs.shape}\")\n", "print(f\"\\nActions les plus fréquentes:\")\n", "print(df_logs['action'].value_counts().head(10))\n", "print(f\"\\nLogs par programme:\")\n", "print(df_logs['programme'].value_counts())\n", "print(\"\\nAperçu des logs:\")\n", "display(df_logs.head(20))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 9.3 DataFrame des Utilisateurs par Programme"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME DES UTILISATEURS ===\")\n", "print(f\"Dimensions: {df_users.shape}\")\n", "print(f\"\\nUtilisateurs par fonction:\")\n", "print(df_users['fonction'].value_counts())\n", "print(f\"\\nUtilisateurs par programme:\")\n", "print(df_users['programme'].value_counts())\n", "print(\"\\nAperçu des utilisateurs:\")\n", "display(df_users)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 9.4 DataFrame des Statistiques par Programme"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== DATAFRAME DES STATISTIQUES ===\")\n", "print(f\"Dimensions: {df_stats.shape}\")\n", "print(f\"\\nTotal des ventes: {df_stats['ventes'].sum()}\")\n", "print(f\"Chiffre d'affaires total: {df_stats['chiffre_affaire'].sum():,.0f} FCFA\")\n", "print(f\"Recouvrement total: {df_stats['recouvrement'].sum():,.0f} FCFA\")\n", "print(f\"Reste à recouvrer total: {df_stats['reste_a_recouvrer'].sum():,.0f} FCFA\")\n", "print(\"\\nStatistiques par programme:\")\n", "display(df_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 10. <PERSON><PERSON><PERSON> par programme"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== ANALYSE DÉTAILLÉE PAR PROGRAMME ===\")\n", "\n", "for programme_name in df_programmes['programme'].unique():\n", "    print(f\"\\n{'='*50}\")\n", "    print(f\"PROGRAMME: {programme_name}\")\n", "    print(f\"{'='*50}\")\n", "    \n", "    # Logs pour ce programme\n", "    logs_prog = df_logs[df_logs['programme'] == programme_name]\n", "    print(f\"\\nNombre de logs: {len(logs_prog)}\")\n", "    if not logs_prog.empty:\n", "        print(\"Actions principales:\")\n", "        print(logs_prog['action'].value_counts().head(5))\n", "        print(\"\\nDerniers logs:\")\n", "        display(logs_prog.tail(10))\n", "    \n", "    # Utilisateurs pour ce programme\n", "    users_prog = df_users[df_users['programme'] == programme_name]\n", "    print(f\"\\nNombre d'utilisateurs: {len(users_prog)}\")\n", "    if not users_prog.empty:\n", "        print(\"Utilisateurs:\")\n", "        display(users_prog)\n", "    \n", "    # Statistiques pour ce programme\n", "    stats_prog = df_stats[df_stats['programme'] == programme_name]\n", "    if not stats_prog.empty:\n", "        print(\"\\nStatistiques:\")\n", "        display(stats_prog)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 11. Résumé des DataFrames créés"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== R<PERSON><PERSON><PERSON><PERSON> DES DATAFRAMES CRÉÉS ===\")\n", "print(f\"\\n1. DataFrame Parent: {df_parent.shape[0]} lignes, {df_parent.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_parent.columns)}\")\n", "\n", "print(f\"\\n2. DataFrame Programmes: {df_programmes.shape[0]} lignes, {df_programmes.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_programmes.columns)}\")\n", "\n", "print(f\"\\n3. DataFrame Logs: {df_logs.shape[0]} lignes, {df_logs.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_logs.columns)}\")\n", "\n", "print(f\"\\n4. DataFrame Users: {df_users.shape[0]} lignes, {df_users.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_users.columns)}\")\n", "\n", "print(f\"\\n5. DataFrame Stats: {df_stats.shape[0]} lignes, {df_stats.shape[1]} colonnes\")\n", "print(f\"   Colonnes: {list(df_stats.columns)}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 5}