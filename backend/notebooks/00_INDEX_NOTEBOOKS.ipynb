# Imports nécessaires pour toutes les conversions
import pandas as pd
import json
import os
from datetime import datetime

def convert_json_to_csv(json_file_path, csv_output_path, dataset_name):
    """Fonction utilitaire pour convertir JSON en CSV"""
    try:
        # Charger le fichier JSON
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Convertir en DataFrame
        if isinstance(data, list):
            df = pd.DataFrame(data)
        elif isinstance(data, dict):
            df = pd.DataFrame([data])
        else:
            df = pd.DataFrame(data)
        
        # Créer le dossier de destination s'il n'existe pas
        os.makedirs(os.path.dirname(csv_output_path), exist_ok=True)
        
        # Sauvegarder en CSV
        df.to_csv(csv_output_path, index=False, encoding='utf-8')
        
        print(f"✓ {dataset_name}: {len(df)} lignes converties")
        print(f"  JSON: {json_file_path}")
        print(f"  CSV:  {csv_output_path}")
        print()
        
        return True
    except Exception as e:
        print(f"✗ Erreur pour {dataset_name}: {str(e)}")
        return False

# 1. CONVERSION - UserProfile Analysis
convert_json_to_csv(
    json_file_path='./Json/userprofile_data.json',  # Ajout du ./
    csv_output_path='./userprofile_data.csv',       # Chemin relatif explicite
    dataset_name='UserProfile Data'
)

# 2. CONVERSION - DQE Data Analysis
convert_json_to_csv(
    json_file_path='Json/dqedata_data.json',
    csv_output_path='Creation_definition_projet/dqedata_data.csv',
    dataset_name='DQE Data'
)

# 3. CONVERSION - École des Talents
convert_json_to_csv(
    json_file_path='Json/ecole_talents_data.json',
    csv_output_path='Suivie_projet/ecole_talents_data.csv',
    dataset_name='École des Talents'
)

# 4. CONVERSION - G-Projet Analysis
convert_json_to_csv(
    json_file_path='Json/gprojet_data.json',
    csv_output_path='Suivie_projet/gprojet_data.csv',
    dataset_name='G-Projet Data'
)

# 5. CONVERSION - G-Stock Approvisionnement
convert_json_to_csv(
    json_file_path='Json/g_stockapprovisionnement.json',
    csv_output_path='Suivie_projet/g_stockapprovisionnement.csv',
    dataset_name='G-Stock Approvisionnement'
)

# 6. CONVERSION - G-Stock Achat
convert_json_to_csv(
    json_file_path='Json/gstockachat_data.json',
    csv_output_path='Suivie_projet/gstockachat_data.csv',
    dataset_name='G-Stock Achat'
)

# 7. CONVERSION - G-Stock Consommation
convert_json_to_csv(
    json_file_path='Json/gstockconsommation_data.json',
    csv_output_path='Suivie_projet/gstockconsommation_data.csv',
    dataset_name='G-Stock Consommation'
)

# 8. CONVERSION - G-Stock Sortie
convert_json_to_csv(
    json_file_path='Json/gstocksortie_data.json',
    csv_output_path='Suivie_projet/gstocksortie_data.csv',
    dataset_name='G-Stock Sortie'
)

# 9. CONVERSION - Données Inflation
convert_json_to_csv(
    json_file_path='Json/donneesinflation_data.json',
    csv_output_path='Donnees externes/donneesinflation_data.csv',
    dataset_name='Données Inflation'
)

# 10. CONVERSION - Données Matériaux Construction
convert_json_to_csv(
    json_file_path='Json/donneesmateriauxconstruction_data.json',
    csv_output_path='Donnees externes/donneesmateriauxconstruction_data.csv',
    dataset_name='Données Matériaux Construction'
)

# 11. CONVERSION - Données Migration Interne
convert_json_to_csv(
    json_file_path='Json/donneesmigrationinterne_data.json',
    csv_output_path='Donnees externes/donneesmigrationinterne_data.csv',
    dataset_name='Données Migration Interne'
)

# 12. CONVERSION - Données Prix Mètre Carré
convert_json_to_csv(
    json_file_path='Json/donneesprixmetrecarre_data.json',
    csv_output_path='Donnees externes/donneesprixmetrecarre_data.csv',
    dataset_name='Données Prix Mètre Carré'
)

# 13. CONVERSION - Données Produit Intérieur Brut
convert_json_to_csv(
    json_file_path='Json/donneesproduitinterieurbrut_data.json',
    csv_output_path='Donnees externes/donneesproduitinterieurbrut_data.csv',
    dataset_name='Données Produit Intérieur Brut'
)

# 14. CONVERSION - Données Projection Démographique
convert_json_to_csv(
    json_file_path='Json/donneesprojectiondemographique_data.json',
    csv_output_path='Donnees externes/donneesprojectiondemographique_data.csv',
    dataset_name='Données Projection Démographique'
)

# 15. CONVERSION - Données Taux Prêt Immobilier
convert_json_to_csv(
    json_file_path='Json/donnestauxpretimmobilier_data.json',
    csv_output_path='Donnees externes/donnestauxpretimmobilier_data.csv',
    dataset_name='Données Taux Prêt Immobilier'
)

# 16. CONVERSION - E-Syndic
convert_json_to_csv(
    json_file_path='./Json/esindic_new.json',
    csv_output_path='./E_syndic.csv',
    dataset_name='E-Syndic'
)

# 17. CONVERSION - Gestion Locative
convert_json_to_csv(
    json_file_path='./Json/G_locative.json',
    csv_output_path='./G_locative.csv',
    dataset_name='Gestion Locative'
)

# 18. CONVERSION - Sentinelle
convert_json_to_csv(
    json_file_path='./Json/sentinelle_data.json',
    csv_output_path='Suivie_projet/sentinelle_data.csv',
    dataset_name='Sentinelle'
)

# 19. CONVERSION - CRM
convert_json_to_csv(
    json_file_path='./Json/crm_data.json',
    csv_output_path='Presentation_vente/crm_data.csv',
    dataset_name='CRM'
)

# RÉSUMÉ DE LA CONVERSION
print("=" * 50)
print("RÉSUMÉ DE LA CONVERSION JSON VERS CSV")
print("=" * 50)
print(f"Fin de la conversion: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print()
print("FICHIERS CSV GÉNÉRÉS DANS LES DOSSIERS:")
print("• Racine notebooks/: userprofile_data.csv, E_syndic.csv, G_locative.csv")
print("• Creation_definition_projet/: dqedata_data.csv")
print("• Suivie_projet/: ecole_talents_data.csv, gprojet_data.csv, g_stockapprovisionnement.csv")
print("                  gstockachat_data.csv, gstockconsommation_data.csv, gstocksortie_data.csv")
print("• Donnees externes/: donneesinflation_data.csv, donneesmateriauxconstruction_data.csv")
print("                     donneesmigrationinterne_data.csv, donneesprixmetrecarre_data.csv")
print("                     donneesproduitinterieurbrut_data.csv, donneesprojectiondemographique_data.csv")
print("                     donnestauxpretimmobilier_data.csv")
print()
print("✓ Tous les fichiers JSON ont été convertis en CSV")
print("✓ Les fichiers CSV sont maintenant disponibles dans leurs dossiers respectifs")
print("✓ Vous pouvez maintenant utiliser les notebooks individuels pour l'analyse")
print()
print("PROCHAINES ÉTAPES:")
print("1. Ouvrir les notebooks individuels dans leurs dossiers")
print("2. Les fichiers CSV seront automatiquement chargés")
print("3. Exécuter les analyses et visualisations Plotly")
print()
print("*Conversion terminée - Kaydan Analytics Hub*")