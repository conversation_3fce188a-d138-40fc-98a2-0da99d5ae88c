{"cells": [{"cell_type": "markdown", "id": "1b1e4685", "metadata": {}, "source": ["# Analyse des données Sentinelle\n", "\n", "Ce notebook analyse les données de suivi des programmes immobiliers Sentinelle."]}, {"cell_type": "markdown", "id": "imports", "metadata": {}, "source": ["## Liste des imports"]}, {"cell_type": "code", "execution_count": 16, "id": "5d2c2f97", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import ast\n", "import numpy as np\n", "from datetime import datetime"]}, {"cell_type": "markdown", "id": "0fd1c035", "metadata": {}, "source": ["## Chargement du fichier CSV"]}, {"cell_type": "code", "execution_count": 17, "id": "3532455d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fichier CSV chargé avec succès: sentinelle_data.csv\n", "Aperçu des données brutes:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>status</th>\n", "      <th>message</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>success</td>\n", "      <td>Données Sentinelle récupérées et sauvegardées</td>\n", "      <td>{'id': 1, 'title': 'Sentinelle Data Import', '...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    status                                        message  \\\n", "0  success  Données Sentinelle récupérées et sauvegardées   \n", "\n", "                                                data  \n", "0  {'id': 1, 'title': 'Sentinelle Data Import', '...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["csv_file = 'sentinelle_data.csv'\n", "\n", "try:\n", "    df_raw = pd.read_csv(csv_file)\n", "    print(f\"Fichier CSV chargé avec succès: {csv_file}\")\n", "    print(\"Aperçu des données brutes:\")\n", "    display(df_raw.head())\n", "    \n", "except FileNotFoundError:\n", "    print(f\"Erreur: <PERSON><PERSON>er {csv_file} non trouvé\")\n", "    print(\"Vérifiez le chemin du fichier et réessayez\")\n", "except Exception as e:\n", "    print(f\"Erreur lors du chargement: {str(e)}\")"]}, {"cell_type": "markdown", "id": "b8b7b1e8", "metadata": {}, "source": ["## Parsing des données et création du DataFrame parent"]}, {"cell_type": "code", "execution_count": 18, "id": "f6ff5d66-e831-46b0-9728-5795b77f9431", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Parsing réussi\n", "DataFrame parent créé:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>import_id</th>\n", "      <th>import_title</th>\n", "      <th>import_description</th>\n", "      <th>data_status</th>\n", "      <th>nb_programmes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Sentinelle Data Import</td>\n", "      <td>Import automatique du 2025-07-25 15:40:58.174966</td>\n", "      <td>True</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   import_id            import_title  \\\n", "0          1  Sentinelle Data Import   \n", "\n", "                                 import_description  data_status  \\\n", "0  Import automatique du 2025-07-25 15:40:58.174966         True   \n", "\n", "   nb_programmes  \n", "0              5  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["def parse_json_data(json_str):\n", "    \"\"\"Parse la chaîne JSON des données\"\"\"\n", "    parsed = ast.literal_eval(json_str)\n", "    print(\"Parsing réussi\")\n", "    return parsed\n", "\n", "# Parsing des données depuis le CSV\n", "parsed_data = parse_json_data(df_raw['data'].iloc[0])\n", "\n", "# Extraction des données principales\n", "main_data = parsed_data\n", "inner_data = main_data['data']\n", "\n", "# Création du DataFrame parent avec les informations principales\n", "df_parent = pd.DataFrame({\n", "    'import_id': [main_data['id']],\n", "    'import_title': [main_data['title']],\n", "    'import_description': [main_data['description']],\n", "    'data_status': [inner_data['status']],\n", "    'nb_programmes': [len(inner_data['taux_programme'])]\n", "})\n", "\n", "print(\"DataFrame parent créé:\")\n", "display(df_parent)"]}, {"cell_type": "markdown", "id": "data_cleaning", "metadata": {}, "source": ["## Data Cleaning - Suppression des colonnes non nécessaires"]}, {"cell_type": "code", "execution_count": 19, "id": "data_cleaning_code", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Nettoyage des données effectué lors de la création du DataFrame\n", "Colonnes conservées dans le DataFrame parent:\n", "['import_id', 'import_title', 'import_description', 'data_status', 'nb_programmes']\n"]}], "source": ["# Les colonnes à supprimer sont déjà exclues lors de la création du DataFrame parent\n", "# (status, message, id du niveau racine et title, description du niveau data)\n", "print(\"Nettoyage des données effectué lors de la création du DataFrame\")\n", "print(\"Colonnes conservées dans le DataFrame parent:\")\n", "print(list(df_parent.columns))"]}, {"cell_type": "markdown", "id": "functions", "metadata": {}, "source": ["## Fonctions d'éclatement des données"]}, {"cell_type": "code", "execution_count": 20, "id": "functions_code", "metadata": {}, "outputs": [], "source": ["def extract_programmes_data(data):\n", "    \"\"\"\n", "    Extrait et nettoie les données des programmes\n", "    \"\"\"\n", "    programmes = []\n", "    \n", "    for programme in data['taux_programme']:\n", "        # Suppression de la colonne 'id' et renommage de 'libelle_programme'\n", "        prog_clean = {\n", "            'id_programme': programme['id_programme'],\n", "            'nom_programme': programme['libelle_programme'].title(),  # Première lettre en majuscule\n", "            'date_saisie': programme['date_saisie'],\n", "            'avancement_global': programme['avancement_global'],\n", "            'valeur_hypothecaire_total': programme['valeur_hypothecaire_total'],\n", "            'nb_lots': len(programme['lots'])\n", "        }\n", "        programmes.append(prog_clean)\n", "    \n", "    return pd.DataFrame(programmes)\n", "\n", "def extract_lots_data(data):\n", "    \"\"\"\n", "    Extrait les données des lots pour chaque programme\n", "    \"\"\"\n", "    lots = []\n", "    \n", "    for programme in data['taux_programme']:\n", "        programme_nom = programme['libelle_programme'].title()\n", "        \n", "        for lot in programme['lots']:\n", "            lot_data = {\n", "                'programme': programme_nom,\n", "                'id_programme': programme['id_programme'],\n", "                'lot': lot['lot'],\n", "                'ilot': lot['ilot'],\n", "                'client': lot['client'],\n", "                'matricule': lot['matricule'],\n", "                'email': lot['email'],\n", "                'tel1': lot['tel1'],\n", "                'type_actif': lot['type_actif'],\n", "                'valeur_hypothecaire': lot['valeur_hypothecaire'],\n", "                'avancement_travaux_mois': lot['avancement_travaux_mois'],\n", "                'cout_actif': lot['cout_actif'],\n", "                'paiement_en_date': lot['paiement_en_date'],\n", "                'reste_a_payer': lot['reste_a_payer']\n", "            }\n", "            lots.append(lot_data)\n", "    \n", "    return pd.DataFrame(lots)"]}, {"cell_type": "markdown", "id": "dataframes", "metadata": {}, "source": ["## Création des DataFrames éclatés"]}, {"cell_type": "code", "execution_count": 21, "id": "create_programmes_df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame des Programmes:\n", "Nombre de programmes: 5\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id_programme</th>\n", "      <th>nom_programme</th>\n", "      <th>date_saisie</th>\n", "      <th>avancement_global</th>\n", "      <th>valeur_hypothecaire_total</th>\n", "      <th>nb_lots</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5</td>\n", "      <td>Cite Bo'Real</td>\n", "      <td>06-2025</td>\n", "      <td>60</td>\n", "      <td>7,224,961,207</td>\n", "      <td>115</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>6</td>\n", "      <td>Cite <PERSON></td>\n", "      <td>06-2025</td>\n", "      <td>27</td>\n", "      <td>7,014,403,608</td>\n", "      <td>87</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7</td>\n", "      <td>Bo'Center</td>\n", "      <td>06-2025</td>\n", "      <td>16</td>\n", "      <td>6,356,146,806</td>\n", "      <td>181</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8</td>\n", "      <td>Les Residences Kotibe</td>\n", "      <td>06-2025</td>\n", "      <td>20</td>\n", "      <td>2,585,655,585</td>\n", "      <td>147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9</td>\n", "      <td>Residences Les Jardins D'Ahoue</td>\n", "      <td>06-2025</td>\n", "      <td>53</td>\n", "      <td>3,501,102,394</td>\n", "      <td>29</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  id_programme                   nom_programme date_saisie avancement_global  \\\n", "0            5                    Cite Bo'Real     06-2025                60   \n", "1            6                  Cite Bo'Re<PERSON>     06-2025                27   \n", "2            7                       Bo'Center     06-2025                16   \n", "3            8           Les Residences Kotibe     06-2025                20   \n", "4            9  Residences Les Jardins D'Ahoue     06-2025                53   \n", "\n", "  valeur_hypothecaire_total  nb_lots  \n", "0             7,224,961,207      115  \n", "1             7,014,403,608       87  \n", "2             6,356,146,806      181  \n", "3             2,585,655,585      147  \n", "4             3,501,102,394       29  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Création du DataFrame des programmes\n", "df_programmes = extract_programmes_data(inner_data)\n", "\n", "print(\"DataFrame des Programmes:\")\n", "print(f\"Nombre de programmes: {len(df_programmes)}\")\n", "display(df_programmes)"]}, {"cell_type": "code", "execution_count": 22, "id": "create_lots_df", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DataFrame des Lots:\n", "Nombre total de lots: 559\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>programme</th>\n", "      <th>id_programme</th>\n", "      <th>lot</th>\n", "      <th>ilot</th>\n", "      <th>client</th>\n", "      <th>matricule</th>\n", "      <th>email</th>\n", "      <th>tel1</th>\n", "      <th>type_actif</th>\n", "      <th>valeur_hypothecaire</th>\n", "      <th>avancement_travaux_mois</th>\n", "      <th>cout_actif</th>\n", "      <th>paiement_en_date</th>\n", "      <th>reste_a_payer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Cite Bo'Real</td>\n", "      <td>5</td>\n", "      <td>252</td>\n", "      <td>66</td>\n", "      <td>WOGNIN JEAN -CLAUDE</td>\n", "      <td>C000499</td>\n", "      <td><EMAIL></td>\n", "      <td>0545005082</td>\n", "      <td>DUPLEX JUMÉLÉ  4 PIÈCES</td>\n", "      <td>60414611.519</td>\n", "      <td>75</td>\n", "      <td>107550000</td>\n", "      <td>0</td>\n", "      <td>107550000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Cite Bo'Real</td>\n", "      <td>5</td>\n", "      <td>251</td>\n", "      <td>66</td>\n", "      <td>DIOMANDE TIEMOKO</td>\n", "      <td>C000106</td>\n", "      <td><EMAIL></td>\n", "      <td>0707418209</td>\n", "      <td>DUPLEX JUMÉLÉ  4 PIÈCES</td>\n", "      <td>58622143.021</td>\n", "      <td>71</td>\n", "      <td>156435256</td>\n", "      <td>0</td>\n", "      <td>156435256</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Cite Bo'Real</td>\n", "      <td>5</td>\n", "      <td>250</td>\n", "      <td>66</td>\n", "      <td>GOSSE PIERRE MICHEL</td>\n", "      <td>C000504</td>\n", "      <td><EMAIL></td>\n", "      <td>0759936584</td>\n", "      <td>DUPLEX JUMÉLÉ  4 PIÈCES</td>\n", "      <td>58381470.623</td>\n", "      <td>75</td>\n", "      <td>70075481</td>\n", "      <td>0</td>\n", "      <td>70075481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Cite Bo'Real</td>\n", "      <td>5</td>\n", "      <td>249</td>\n", "      <td>66</td>\n", "      <td>M'BRA KOUADIO DONATIEN</td>\n", "      <td>C000589</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>56848831.871</td>\n", "      <td>72</td>\n", "      <td>50000000</td>\n", "      <td>0</td>\n", "      <td>50000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Cite Bo'Real</td>\n", "      <td>5</td>\n", "      <td>248</td>\n", "      <td>66</td>\n", "      <td>SISSE BAZOUMANA</td>\n", "      <td>C000594</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>56657191.257</td>\n", "      <td>72</td>\n", "      <td>50000000</td>\n", "      <td>5000000</td>\n", "      <td>45000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>554</th>\n", "      <td>Residences Les Jardins D'Ahoue</td>\n", "      <td>9</td>\n", "      <td>195</td>\n", "      <td>10</td>\n", "      <td>SCI AURORE BOREALE</td>\n", "      <td>C000171</td>\n", "      <td>y_<PERSON><PERSON><PERSON>@hotmail.fr</td>\n", "      <td>0708100780</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>42788849.396</td>\n", "      <td>68</td>\n", "      <td>67500000</td>\n", "      <td>24000000</td>\n", "      <td>43500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>555</th>\n", "      <td>Residences Les Jardins D'Ahoue</td>\n", "      <td>9</td>\n", "      <td>196</td>\n", "      <td>10</td>\n", "      <td>SCI SAUYET</td>\n", "      <td>C000724</td>\n", "      <td>b<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com</td>\n", "      <td>07 89 96 20 78</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>43093702.396</td>\n", "      <td>69</td>\n", "      <td>67500000</td>\n", "      <td>29000000</td>\n", "      <td>38500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>556</th>\n", "      <td>Residences Les Jardins D'Ahoue</td>\n", "      <td>9</td>\n", "      <td>198</td>\n", "      <td>10</td>\n", "      <td>VALLET SAMAN ANNE FRANCOISE</td>\n", "      <td>C000716</td>\n", "      <td><EMAIL></td>\n", "      <td>07 07 93 92 82</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>41359076.672</td>\n", "      <td>65</td>\n", "      <td>67500000</td>\n", "      <td>27250000</td>\n", "      <td>40250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>557</th>\n", "      <td>Residences Les Jardins D'Ahoue</td>\n", "      <td>9</td>\n", "      <td>199</td>\n", "      <td>10</td>\n", "      <td>TOURE KOANIMAN</td>\n", "      <td>C000814</td>\n", "      <td><EMAIL></td>\n", "      <td>01 01 00 66 65</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>41040140.2894</td>\n", "      <td>64</td>\n", "      <td>67500000</td>\n", "      <td>32500000</td>\n", "      <td>35000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>558</th>\n", "      <td>Residences Les Jardins D'Ahoue</td>\n", "      <td>9</td>\n", "      <td>200</td>\n", "      <td>10</td>\n", "      <td>ZOZO-BOLI LAURENT &amp; PATRICIA ANNICK</td>\n", "      <td>C000793</td>\n", "      <td><EMAIL></td>\n", "      <td>05 05 05 21 79</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>40995852.553</td>\n", "      <td>64</td>\n", "      <td>67500000</td>\n", "      <td>25999900</td>\n", "      <td>41500100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>559 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                          programme id_programme  lot ilot  \\\n", "0                      Cite Bo'Real            5  252   66   \n", "1                      Cite Bo'Real            5  251   66   \n", "2                      Cite Bo'Real            5  250   66   \n", "3                      Cite Bo'Real            5  249   66   \n", "4                      Cite Bo'Real            5  248   66   \n", "..                              ...          ...  ...  ...   \n", "554  Residences Les Jardins D'Ahoue            9  195   10   \n", "555  Residences Les Jardins D'Ahoue            9  196   10   \n", "556  Residences Les Jardins D'Ahoue            9  198   10   \n", "557  Residences Les Jardins D'Ahoue            9  199   10   \n", "558  Residences Les Jardins D'Ahoue            9  200   10   \n", "\n", "                                  client matricule  \\\n", "0                    WOGNIN JEAN -CLAUDE   C000499   \n", "1                       DIOMANDE TIEMOKO   C000106   \n", "2                    GOSSE PIERRE MICHEL   C000504   \n", "3                 M'BRA KOUADIO DONATIEN   C000589   \n", "4                        SISSE BAZOUMANA   C000594   \n", "..                                   ...       ...   \n", "554                  SCI AURORE BOREALE    C000171   \n", "555                           SCI SAUYET   C000724   \n", "556          VALLET SAMAN ANNE FRANCOISE   C000716   \n", "557                       TOURE KOANIMAN   C000814   \n", "558  ZOZO-BOLI LAURENT & PATRICIA ANNICK   C000793   \n", "\n", "                             email            tel1               type_actif  \\\n", "0               <EMAIL>      0545005082  DUPLEX JUMÉLÉ  4 PIÈCES   \n", "1    <EMAIL>      0707418209  DUPLEX JUMÉLÉ  4 PIÈCES   \n", "2              <EMAIL>      0759936584  DUPLEX JUMÉLÉ  4 PIÈCES   \n", "3                             None            None          DUPLEX 4 PIÈCES   \n", "4                             None            None          DUPLEX 4 PIÈCES   \n", "..                             ...             ...                      ...   \n", "554          y_<PERSON><PERSON><PERSON>@hotmail.fr      0708100780          DUPLEX 4 PIÈCES   \n", "555       b<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com  07 89 96 20 78          DUPLEX 4 PIÈCES   \n", "556     <EMAIL>  07 07 93 92 82          DUPLEX 4 PIÈCES   \n", "557          <EMAIL>  01 01 00 66 65          DUPLEX 4 PIÈCES   \n", "558          <EMAIL>  05 05 05 21 79          DUPLEX 4 PIÈCES   \n", "\n", "    valeur_hypothecaire avancement_travaux_mois cout_actif  paiement_en_date  \\\n", "0          60414611.519                      75  107550000                 0   \n", "1          58622143.021                      71  156435256                 0   \n", "2          58381470.623                      75   70075481                 0   \n", "3          56848831.871                      72   50000000                 0   \n", "4          56657191.257                      72   50000000           5000000   \n", "..                  ...                     ...        ...               ...   \n", "554        42788849.396                      68   67500000          24000000   \n", "555        43093702.396                      69   67500000          29000000   \n", "556        41359076.672                      65   67500000          27250000   \n", "557       41040140.2894                      64   67500000          32500000   \n", "558        40995852.553                      64   67500000          25999900   \n", "\n", "     reste_a_payer  \n", "0        107550000  \n", "1        156435256  \n", "2         70075481  \n", "3         50000000  \n", "4         45000000  \n", "..             ...  \n", "554       43500000  \n", "555       38500000  \n", "556       40250000  \n", "557       35000000  \n", "558       41500100  \n", "\n", "[559 rows x 14 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Création du DataFrame des lots\n", "df_lots = extract_lots_data(inner_data)\n", "\n", "print(\"DataFrame des Lots:\")\n", "print(f\"Nombre total de lots: {len(df_lots)}\")\n", "display(df_lots)"]}, {"cell_type": "markdown", "id": "analysis", "metadata": {}, "source": ["## Fonctions d'analyse"]}, {"cell_type": "code", "execution_count": 23, "id": "analysis_functions", "metadata": {}, "outputs": [], "source": ["def analyze_programmes(df_programmes):\n", "    \"\"\"\n", "    Analyse des programmes par année\n", "    \"\"\"\n", "    # Extraction de l'année à partir de date_saisie\n", "    df_programmes['annee'] = df_programmes['date_saisie'].str.split('-').str[1]\n", "    \n", "    analysis = {\n", "        'total_programmes': len(df_programmes),\n", "        'programmes_par_annee': df_programmes['annee'].value_counts().sort_index(),\n", "        'avancement_moyen': df_programmes['avancement_global'].astype(float).mean(),\n", "        'valeur_totale': df_programmes['valeur_hypothecaire_total'].str.replace(',', '').astype(float).sum()\n", "    }\n", "    \n", "    return analysis\n", "\n", "def analyze_lots_by_programme(df_lots):\n", "    \"\"\"\n", "    Analyse des lots par programme\n", "    \"\"\"\n", "    analysis = df_lots.groupby('programme').agg({\n", "        'lot': 'count',\n", "        'avancement_travaux_mois': lambda x: x.astype(float).mean(),\n", "        'cout_actif': lambda x: x.astype(float).sum(),\n", "        'paiement_en_date': lambda x: x.astype(float).sum(),\n", "        'reste_a_payer': lambda x: x.astype(float).sum()\n", "    }).round(2)\n", "    \n", "    analysis.columns = ['Nb_lots', 'Avancement_moyen', 'Cout_total', 'Paiements_total', 'Reste_a_payer_total']\n", "    \n", "    return analysis"]}, {"cell_type": "code", "execution_count": 24, "id": "lots_by_programme_display", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== DÉTAIL DES LOTS PAR PROGRAMME ===\n", "\n", "--- Programme: Cite Bo'Real ---\n", "Nombre de lots: 115\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lot</th>\n", "      <th>ilot</th>\n", "      <th>client</th>\n", "      <th>type_actif</th>\n", "      <th>avancement_travaux_mois</th>\n", "      <th>cout_actif</th>\n", "      <th>paiement_en_date</th>\n", "      <th>reste_a_payer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>252</td>\n", "      <td>66</td>\n", "      <td>WOGNIN JEAN -CLAUDE</td>\n", "      <td>DUPLEX JUMÉLÉ  4 PIÈCES</td>\n", "      <td>75</td>\n", "      <td>107550000</td>\n", "      <td>0</td>\n", "      <td>107550000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>251</td>\n", "      <td>66</td>\n", "      <td>DIOMANDE TIEMOKO</td>\n", "      <td>DUPLEX JUMÉLÉ  4 PIÈCES</td>\n", "      <td>71</td>\n", "      <td>156435256</td>\n", "      <td>0</td>\n", "      <td>156435256</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>250</td>\n", "      <td>66</td>\n", "      <td>GOSSE PIERRE MICHEL</td>\n", "      <td>DUPLEX JUMÉLÉ  4 PIÈCES</td>\n", "      <td>75</td>\n", "      <td>70075481</td>\n", "      <td>0</td>\n", "      <td>70075481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>249</td>\n", "      <td>66</td>\n", "      <td>M'BRA KOUADIO DONATIEN</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>72</td>\n", "      <td>50000000</td>\n", "      <td>0</td>\n", "      <td>50000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>248</td>\n", "      <td>66</td>\n", "      <td>SISSE BAZOUMANA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>72</td>\n", "      <td>50000000</td>\n", "      <td>5000000</td>\n", "      <td>45000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>220</td>\n", "      <td>59</td>\n", "      <td>BAMBA DRISSA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>52</td>\n", "      <td>60000000</td>\n", "      <td>9000000</td>\n", "      <td>51000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>219</td>\n", "      <td>59</td>\n", "      <td>COULIBALY NINA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>57</td>\n", "      <td>117000000</td>\n", "      <td>20650000</td>\n", "      <td>96350000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>218</td>\n", "      <td>59</td>\n", "      <td>KABA YAYA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>55</td>\n", "      <td>123000000</td>\n", "      <td>11365000</td>\n", "      <td>111635000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>113</th>\n", "      <td>217</td>\n", "      <td>59</td>\n", "      <td>CISSE BEN VAMISSA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>54</td>\n", "      <td>120000000</td>\n", "      <td>120650000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>216</td>\n", "      <td>59</td>\n", "      <td>TRAORE AICHA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>54</td>\n", "      <td>120000000</td>\n", "      <td>91650000</td>\n", "      <td>28350000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>115 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     lot ilot                  client               type_actif  \\\n", "0    252   66     WOGNIN JEAN -CLAUDE  DUPLEX JUMÉLÉ  4 PIÈCES   \n", "1    251   66        DIOMANDE TIEMOKO  DUPLEX JUMÉLÉ  4 PIÈCES   \n", "2    250   66     GOSSE PIERRE MICHEL  DUPLEX JUMÉLÉ  4 PIÈCES   \n", "3    249   66  M'BRA KOUADIO DONATIEN          DUPLEX 4 PIÈCES   \n", "4    248   66         SISSE BAZOUMANA          DUPLEX 4 PIÈCES   \n", "..   ...  ...                     ...                      ...   \n", "110  220   59            BAMBA DRISSA          DUPLEX 4 PIÈCES   \n", "111  219   59          COULIBALY NINA          DUPLEX 4 PIÈCES   \n", "112  218   59               KABA YAYA          DUPLEX 4 PIÈCES   \n", "113  217   59       CISSE BEN VAMISSA          DUPLEX 4 PIÈCES   \n", "114  216   59            TRAORE AICHA          DUPLEX 4 PIÈCES   \n", "\n", "    avancement_travaux_mois cout_actif  paiement_en_date  reste_a_payer  \n", "0                        75  107550000                 0      107550000  \n", "1                        71  156435256                 0      156435256  \n", "2                        75   70075481                 0       70075481  \n", "3                        72   50000000                 0       50000000  \n", "4                        72   50000000           5000000       45000000  \n", "..                      ...        ...               ...            ...  \n", "110                      52   60000000           9000000       51000000  \n", "111                      57  117000000          20650000       96350000  \n", "112                      55  123000000          11365000      111635000  \n", "113                      54  120000000         120650000              0  \n", "114                      54  120000000          91650000       28350000  \n", "\n", "[115 rows x 8 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Programme: Cite <PERSON> ---\n", "Nombre de lots: 87\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lot</th>\n", "      <th>ilot</th>\n", "      <th>client</th>\n", "      <th>type_actif</th>\n", "      <th>avancement_travaux_mois</th>\n", "      <th>cout_actif</th>\n", "      <th>paiement_en_date</th>\n", "      <th>reste_a_payer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>115</th>\n", "      <td>11</td>\n", "      <td>3</td>\n", "      <td>SCI ESYCA</td>\n", "      <td>DUPLEX 5 PIÈCES</td>\n", "      <td>23</td>\n", "      <td>176800000</td>\n", "      <td>365000000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116</th>\n", "      <td>12</td>\n", "      <td>3</td>\n", "      <td>KOUAME TEYA ANDERSON</td>\n", "      <td>DUPLEX 5 PIÈCES</td>\n", "      <td>18</td>\n", "      <td>184150000</td>\n", "      <td>184800000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>117</th>\n", "      <td>13</td>\n", "      <td>3</td>\n", "      <td>TAPE KETSIA</td>\n", "      <td>DUPLEX JUMÉLÉ  5 PIÈCES</td>\n", "      <td>23</td>\n", "      <td>181100000</td>\n", "      <td>155403141</td>\n", "      <td>25696859</td>\n", "    </tr>\n", "    <tr>\n", "      <th>118</th>\n", "      <td>14</td>\n", "      <td>3</td>\n", "      <td>AFEMADY SCI</td>\n", "      <td>DUPLEX 5 PIÈCES</td>\n", "      <td>24</td>\n", "      <td>165000000</td>\n", "      <td>80000000</td>\n", "      <td>85000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>119</th>\n", "      <td>39</td>\n", "      <td>11</td>\n", "      <td>SAME HERVE JONATHAN</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>28</td>\n", "      <td>165000000</td>\n", "      <td>0</td>\n", "      <td>165000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>197</th>\n", "      <td>53</td>\n", "      <td>12</td>\n", "      <td>SCI KERLAU CI</td>\n", "      <td>Villa Duplex 6 pièces Individuelle</td>\n", "      <td>27</td>\n", "      <td>267000000</td>\n", "      <td>173550000</td>\n", "      <td>93450000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>52</td>\n", "      <td>12</td>\n", "      <td>SCI AURORE BOREALE</td>\n", "      <td>Villa Duplex 6 pièces Individuelle</td>\n", "      <td>33</td>\n", "      <td>225000000</td>\n", "      <td>60000000</td>\n", "      <td>165000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>51</td>\n", "      <td>12</td>\n", "      <td>SOULEY ACHABI AKOUAVI</td>\n", "      <td>Villa Duplex 6 pièces Individuelle</td>\n", "      <td>30</td>\n", "      <td>263000000</td>\n", "      <td>135000000</td>\n", "      <td>128000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>50</td>\n", "      <td>12</td>\n", "      <td>M ET MME AHONON</td>\n", "      <td>Villa Duplex 6 pièces Individuelle</td>\n", "      <td>30</td>\n", "      <td>243600000</td>\n", "      <td>184880000</td>\n", "      <td>58720000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>49</td>\n", "      <td>12</td>\n", "      <td>CISSE &amp; DOUMBIA MAHAMADOU SADIKOU &amp; IDRISSA</td>\n", "      <td>Villa Duplex 6 pièces Individuelle</td>\n", "      <td>31</td>\n", "      <td>223600000</td>\n", "      <td>179530000</td>\n", "      <td>44070000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>87 rows × 8 columns</p>\n", "</div>"], "text/plain": ["    lot ilot                                       client  \\\n", "115  11    3                                    SCI ESYCA   \n", "116  12    3                         KOUAME TEYA ANDERSON   \n", "117  13    3                                  TAPE KETSIA   \n", "118  14    3                                  AFEMADY SCI   \n", "119  39   11                          SAME HERVE JONATHAN   \n", "..   ..  ...                                          ...   \n", "197  53   12                               SCI KERLAU CI    \n", "198  52   12                          SCI AURORE BOREALE    \n", "199  51   12                        SOULEY ACHABI AKOUAVI   \n", "200  50   12                             M ET MME AHONON    \n", "201  49   12  CISSE & DOUMBIA MAHAMADOU SADIKOU & IDRISSA   \n", "\n", "                             type_actif avancement_travaux_mois cout_actif  \\\n", "115                     DUPLEX 5 PIÈCES                      23  176800000   \n", "116                     DUPLEX 5 PIÈCES                      18  184150000   \n", "117             DUPLEX JUMÉLÉ  5 PIÈCES                      23  181100000   \n", "118                     DUPLEX 5 PIÈCES                      24  165000000   \n", "119                     DUPLEX 4 PIÈCES                      28  165000000   \n", "..                                  ...                     ...        ...   \n", "197  Villa Duplex 6 pièces Individuelle                      27  267000000   \n", "198  Villa Duplex 6 pièces Individuelle                      33  225000000   \n", "199  Villa Duplex 6 pièces Individuelle                      30  263000000   \n", "200  Villa Duplex 6 pièces Individuelle                      30  243600000   \n", "201  Villa Duplex 6 pièces Individuelle                      31  223600000   \n", "\n", "     paiement_en_date  reste_a_payer  \n", "115         365000000              0  \n", "116         184800000              0  \n", "117         155403141       25696859  \n", "118          80000000       85000000  \n", "119                 0      165000000  \n", "..                ...            ...  \n", "197         173550000       93450000  \n", "198          60000000      165000000  \n", "199         135000000      128000000  \n", "200         184880000       58720000  \n", "201         179530000       44070000  \n", "\n", "[87 rows x 8 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Programme: Bo'Center ---\n", "Nombre de lots: 181\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lot</th>\n", "      <th>ilot</th>\n", "      <th>client</th>\n", "      <th>type_actif</th>\n", "      <th>avancement_travaux_mois</th>\n", "      <th>cout_actif</th>\n", "      <th>paiement_en_date</th>\n", "      <th>reste_a_payer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>5</td>\n", "      <td>2</td>\n", "      <td>SCI PIVOINE BLANCHE</td>\n", "      <td>BLOC A+</td>\n", "      <td>12</td>\n", "      <td>410000000</td>\n", "      <td>0</td>\n", "      <td>410000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>SCI 4 VENTS</td>\n", "      <td>BLOC A</td>\n", "      <td>12</td>\n", "      <td>395000000</td>\n", "      <td>0</td>\n", "      <td>395000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>8</td>\n", "      <td>2</td>\n", "      <td>CISSE &amp; DOUMBIA MAHAMADOU SADIKOU &amp; IDRISSA</td>\n", "      <td>BLOC A</td>\n", "      <td>10</td>\n", "      <td>395000000</td>\n", "      <td>327500000</td>\n", "      <td>67500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>145</td>\n", "      <td>38</td>\n", "      <td>DALLI ARMEL AZIZ ROMEO</td>\n", "      <td>APPARTEMENT TYPE F3</td>\n", "      <td>21</td>\n", "      <td>65000000</td>\n", "      <td>20000000</td>\n", "      <td>45000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>145</td>\n", "      <td>38</td>\n", "      <td>COULIBALY HERVE TIEKOURA</td>\n", "      <td>MAGASIN</td>\n", "      <td>21</td>\n", "      <td>150000000</td>\n", "      <td>33000000</td>\n", "      <td>117000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>378</th>\n", "      <td>158</td>\n", "      <td>43</td>\n", "      <td>AMANGOUA BLEHOUE BENJAMIN</td>\n", "      <td>APPARTEMENT TYPE F4</td>\n", "      <td>44</td>\n", "      <td>36500000</td>\n", "      <td>0</td>\n", "      <td>36500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>379</th>\n", "      <td>158</td>\n", "      <td>43</td>\n", "      <td>ROMBA LACINA</td>\n", "      <td>APPARTEMENT TYPE F4</td>\n", "      <td>44</td>\n", "      <td>73000000</td>\n", "      <td>48950000</td>\n", "      <td>24050000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>380</th>\n", "      <td>158</td>\n", "      <td>43</td>\n", "      <td>N'GUESSAN EPSE GOMEZ DODO FELICIA MARIE</td>\n", "      <td>APPARTEMENT TYPE F4</td>\n", "      <td>44</td>\n", "      <td>73000000</td>\n", "      <td>47950000</td>\n", "      <td>25050000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>381</th>\n", "      <td>158</td>\n", "      <td>43</td>\n", "      <td>KPRAKPRA GEORGES ALAIN CHRISTIAN</td>\n", "      <td>APPARTEMENT TYPE F4</td>\n", "      <td>44</td>\n", "      <td>75000000</td>\n", "      <td>75650000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>382</th>\n", "      <td>158</td>\n", "      <td>43</td>\n", "      <td>TIE LOU epse OUATTARA Josiane Cynthia</td>\n", "      <td>APPARTEMENT TYPE F4</td>\n", "      <td>44</td>\n", "      <td>75000000</td>\n", "      <td>8950000</td>\n", "      <td>66050000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>181 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     lot ilot                                       client  \\\n", "202    5    2                         SCI PIVOINE BLANCHE    \n", "203    7    2                                  SCI 4 VENTS   \n", "204    8    2  CISSE & DOUMBIA MAHAMADOU SADIKOU & IDRISSA   \n", "205  145   38                       DALLI ARMEL AZIZ ROMEO   \n", "206  145   38                     COULIBALY HERVE TIEKOURA   \n", "..   ...  ...                                          ...   \n", "378  158   43                    AMANGOUA BLEHOUE BENJAMIN   \n", "379  158   43                                 ROMBA LACINA   \n", "380  158   43      N'GUESSAN EPSE GOMEZ DODO FELICIA MARIE   \n", "381  158   43             KPRAKPRA GEORGES ALAIN CHRISTIAN   \n", "382  158   43        TIE LOU epse OUATTARA Josiane Cynthia   \n", "\n", "              type_actif avancement_travaux_mois cout_actif  paiement_en_date  \\\n", "202              BLOC A+                      12  410000000                 0   \n", "203               BLOC A                      12  395000000                 0   \n", "204               BLOC A                      10  395000000         327500000   \n", "205  APPARTEMENT TYPE F3                      21   65000000          20000000   \n", "206              MAGASIN                      21  150000000          33000000   \n", "..                   ...                     ...        ...               ...   \n", "378  APPARTEMENT TYPE F4                      44   36500000                 0   \n", "379  APPARTEMENT TYPE F4                      44   73000000          48950000   \n", "380  APPARTEMENT TYPE F4                      44   73000000          47950000   \n", "381  APPARTEMENT TYPE F4                      44   75000000          75650000   \n", "382  APPARTEMENT TYPE F4                      44   75000000           8950000   \n", "\n", "     reste_a_payer  \n", "202      410000000  \n", "203      395000000  \n", "204       67500000  \n", "205       45000000  \n", "206      117000000  \n", "..             ...  \n", "378       36500000  \n", "379       24050000  \n", "380       25050000  \n", "381              0  \n", "382       66050000  \n", "\n", "[181 rows x 8 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Programme: Les Residences Kotibe ---\n", "Nombre de lots: 147\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lot</th>\n", "      <th>ilot</th>\n", "      <th>client</th>\n", "      <th>type_actif</th>\n", "      <th>avancement_travaux_mois</th>\n", "      <th>cout_actif</th>\n", "      <th>paiement_en_date</th>\n", "      <th>reste_a_payer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>383</th>\n", "      <td>138</td>\n", "      <td>15</td>\n", "      <td>DAKOURI GUEHIA GUEHI</td>\n", "      <td>VILLA BASE JUMELÉE 3 PIÈCES</td>\n", "      <td>56</td>\n", "      <td>11160000</td>\n", "      <td>569900</td>\n", "      <td>10590100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>384</th>\n", "      <td>142</td>\n", "      <td>16</td>\n", "      <td>KOUAME ADJOUA ESTER MARIE BIENVENUE</td>\n", "      <td>VILLA BASE JUMELÉE 3 PIÈCES</td>\n", "      <td>36</td>\n", "      <td>11160000</td>\n", "      <td>2499900</td>\n", "      <td>8660100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>385</th>\n", "      <td>152</td>\n", "      <td>17</td>\n", "      <td>KOFFI DIBY ADJOUAH CAROLLE</td>\n", "      <td>VILLA BASE JUMELÉE 3 PIÈCES</td>\n", "      <td>19</td>\n", "      <td>23000000</td>\n", "      <td>4850000</td>\n", "      <td>18150000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>386</th>\n", "      <td>154</td>\n", "      <td>18</td>\n", "      <td>COULIBALY YACOUBA</td>\n", "      <td>VILLA BASE JUMELÉE 3 PIÈCES</td>\n", "      <td>27</td>\n", "      <td>23000000</td>\n", "      <td>0</td>\n", "      <td>23000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>387</th>\n", "      <td>155</td>\n", "      <td>18</td>\n", "      <td></td>\n", "      <td>VILLA BASE JUMELÉE 3 PIÈCES</td>\n", "      <td>27</td>\n", "      <td>22000000</td>\n", "      <td>3550300</td>\n", "      <td>18449700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>525</th>\n", "      <td>50</td>\n", "      <td>5</td>\n", "      <td><PERSON><PERSON>OU DJEDJE RODRIGUE</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>5</td>\n", "      <td>39500000</td>\n", "      <td>1975000</td>\n", "      <td>37525000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>526</th>\n", "      <td>54</td>\n", "      <td>6</td>\n", "      <td>GRAH KOKORA ABY</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>0</td>\n", "      <td>39500000</td>\n", "      <td>2125000</td>\n", "      <td>37375000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>527</th>\n", "      <td>63</td>\n", "      <td>7</td>\n", "      <td>DROH NEE OSSENE SANOUSSI RITA SABRINA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>0</td>\n", "      <td>39500000</td>\n", "      <td>2124900</td>\n", "      <td>37375100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>528</th>\n", "      <td>71</td>\n", "      <td>7</td>\n", "      <td>KOFFI GNALHEY MARC EPHREM</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>19</td>\n", "      <td>70000000</td>\n", "      <td>70500000</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>529</th>\n", "      <td>73</td>\n", "      <td>7</td>\n", "      <td>BRIGHT FELIX EDMOND WADJA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>13</td>\n", "      <td>70000000</td>\n", "      <td>22000000</td>\n", "      <td>48000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>147 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     lot ilot                                 client  \\\n", "383  138   15                   DAKOURI GUEHIA GUEHI   \n", "384  142   16    KOUAME ADJOUA ESTER MARIE BIENVENUE   \n", "385  152   17             KOFFI DIBY ADJOUAH CAROLLE   \n", "386  154   18                      COULIBALY YACOUBA   \n", "387  155   18                                          \n", "..   ...  ...                                    ...   \n", "525   50    5                  YOHOU DJEDJE RODRIGUE   \n", "526   54    6                        GRAH KOKORA ABY   \n", "527   63    7  DROH NEE OSSENE SANOUSSI RITA SABRINA   \n", "528   71    7              KOFFI GNALHEY MARC EPHREM   \n", "529   73    7              BRIGHT FELIX EDMOND WADJA   \n", "\n", "                      type_actif avancement_travaux_mois cout_actif  \\\n", "383  VILLA BASE JUMELÉE 3 PIÈCES                      56   11160000   \n", "384  VILLA BASE JUMELÉE 3 PIÈCES                      36   11160000   \n", "385  VILLA BASE JUMELÉE 3 PIÈCES                      19   23000000   \n", "386  VILLA BASE JUMELÉE 3 PIÈCES                      27   23000000   \n", "387  VILLA BASE JUMELÉE 3 PIÈCES                      27   22000000   \n", "..                           ...                     ...        ...   \n", "525              DUPLEX 4 PIÈCES                       5   39500000   \n", "526              DUPLEX 4 PIÈCES                       0   39500000   \n", "527              DUPLEX 4 PIÈCES                       0   39500000   \n", "528              DUPLEX 4 PIÈCES                      19   70000000   \n", "529              DUPLEX 4 PIÈCES                      13   70000000   \n", "\n", "     paiement_en_date  reste_a_payer  \n", "383            569900       10590100  \n", "384           2499900        8660100  \n", "385           4850000       18150000  \n", "386                 0       23000000  \n", "387           3550300       18449700  \n", "..                ...            ...  \n", "525           1975000       37525000  \n", "526           2125000       37375000  \n", "527           2124900       37375100  \n", "528          70500000              0  \n", "529          22000000       48000000  \n", "\n", "[147 rows x 8 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "--- Programme: Residences Les Jardins D'Ahoue ---\n", "Nombre de lots: 29\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>lot</th>\n", "      <th>ilot</th>\n", "      <th>client</th>\n", "      <th>type_actif</th>\n", "      <th>avancement_travaux_mois</th>\n", "      <th>cout_actif</th>\n", "      <th>paiement_en_date</th>\n", "      <th>reste_a_payer</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>530</th>\n", "      <td>202</td>\n", "      <td>12</td>\n", "      <td>KOUKOU GBALEY KRAGBE ACHILLE NARCISSE</td>\n", "      <td>DUPLEX 3 PIÈCES MIT</td>\n", "      <td>50</td>\n", "      <td>42000000</td>\n", "      <td>16850000</td>\n", "      <td>25150000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>531</th>\n", "      <td>203</td>\n", "      <td>12</td>\n", "      <td>KOUKOU GBALEY KRAGBE ACHILLE NARCISSE</td>\n", "      <td>DUPLEX 3 PIÈCES MIT</td>\n", "      <td>47</td>\n", "      <td>42000000</td>\n", "      <td>15850100</td>\n", "      <td>26149900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>532</th>\n", "      <td>204</td>\n", "      <td>12</td>\n", "      <td>MERESSO YAO AMON CYNTHIA EVELYNE</td>\n", "      <td>DUPLEX 3 PIÈCES MIT</td>\n", "      <td>50</td>\n", "      <td>42000000</td>\n", "      <td>3500000</td>\n", "      <td>38500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>533</th>\n", "      <td>169</td>\n", "      <td>10</td>\n", "      <td>AZAGOH KOUASSI KOUAKOU GERMAIN</td>\n", "      <td>DUPLEX 4 PIÈCES MIT</td>\n", "      <td>68</td>\n", "      <td>60360000</td>\n", "      <td>10272000</td>\n", "      <td>50088000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>534</th>\n", "      <td>229</td>\n", "      <td>18</td>\n", "      <td>TANOE AMON MARIE-CHRISTELLE CHINMINGNON</td>\n", "      <td>DUPLEX 4 PIÈCES MIT</td>\n", "      <td>59</td>\n", "      <td>57000000</td>\n", "      <td>9222000</td>\n", "      <td>47778000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>535</th>\n", "      <td>246</td>\n", "      <td>20</td>\n", "      <td>ISIAKA ET SANOGO ABDOU JOKOOLA ET MATEININ</td>\n", "      <td>DUPLEX 4 PIÈCES MIT</td>\n", "      <td>55</td>\n", "      <td>57000000</td>\n", "      <td>37000000</td>\n", "      <td>20000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>536</th>\n", "      <td>264</td>\n", "      <td>20</td>\n", "      <td>NANA CEDRIC SEBASTIEN NYAKAM NGOKO</td>\n", "      <td>DUPLEX 4 PIÈCES MIT</td>\n", "      <td>63</td>\n", "      <td>57000000</td>\n", "      <td>39000000</td>\n", "      <td>18000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>537</th>\n", "      <td>288</td>\n", "      <td>22</td>\n", "      <td>KONE SITA</td>\n", "      <td>DUPLEX 4 PIÈCES MIT</td>\n", "      <td>44</td>\n", "      <td>59240000</td>\n", "      <td>45500000</td>\n", "      <td>13740000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>538</th>\n", "      <td>289</td>\n", "      <td>22</td>\n", "      <td>OUATTARA BIAKEREBE AICHATOU</td>\n", "      <td>DUPLEX 4 PIÈCES MIT</td>\n", "      <td>40</td>\n", "      <td>59030000</td>\n", "      <td>5500000</td>\n", "      <td>53530000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>539</th>\n", "      <td>290</td>\n", "      <td>22</td>\n", "      <td>COULIBALY MANOUM ISSOUF</td>\n", "      <td>DUPLEX 4 PIÈCES MIT</td>\n", "      <td>39</td>\n", "      <td>60710000</td>\n", "      <td>30000000</td>\n", "      <td>30710000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>540</th>\n", "      <td>181</td>\n", "      <td>10</td>\n", "      <td>SCI NOLETHIKA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>74</td>\n", "      <td>74500000</td>\n", "      <td>15000000</td>\n", "      <td>59500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>541</th>\n", "      <td>182</td>\n", "      <td>10</td>\n", "      <td>JACQUES ARSENE TEGNAN EBBA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>74</td>\n", "      <td>67500000</td>\n", "      <td>17000000</td>\n", "      <td>50500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>542</th>\n", "      <td>183</td>\n", "      <td>10</td>\n", "      <td>GNAGNE-ADOU MARIE-MARCELLE</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>74</td>\n", "      <td>67500000</td>\n", "      <td>14000000</td>\n", "      <td>53500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>543</th>\n", "      <td>184</td>\n", "      <td>10</td>\n", "      <td>VALLET SAMAN ANNE FRANCOISE</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>74</td>\n", "      <td>67500000</td>\n", "      <td>27250000</td>\n", "      <td>40250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>544</th>\n", "      <td>185</td>\n", "      <td>10</td>\n", "      <td>KOUAKOU OFORI FRANÇOIS</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>73</td>\n", "      <td>58387500</td>\n", "      <td>8000000</td>\n", "      <td>50387500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>545</th>\n", "      <td>186</td>\n", "      <td>10</td>\n", "      <td>N'GUETTIA YAO JUNIOR STEPHANE</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>74</td>\n", "      <td>67500000</td>\n", "      <td>26500000</td>\n", "      <td>41000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>546</th>\n", "      <td>187</td>\n", "      <td>10</td>\n", "      <td>SCI NOLETHIKA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>74</td>\n", "      <td>67500000</td>\n", "      <td>15000000</td>\n", "      <td>52500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>547</th>\n", "      <td>188</td>\n", "      <td>10</td>\n", "      <td>SCI NOLETHIKA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>74</td>\n", "      <td>67500000</td>\n", "      <td>15000000</td>\n", "      <td>52500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>548</th>\n", "      <td>189</td>\n", "      <td>10</td>\n", "      <td>SCI NOLETHIKA</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>74</td>\n", "      <td>67500000</td>\n", "      <td>15000000</td>\n", "      <td>52500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>549</th>\n", "      <td>190</td>\n", "      <td>10</td>\n", "      <td>FLEUR DE LYS SCI</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>20</td>\n", "      <td>71420000</td>\n", "      <td>35973334</td>\n", "      <td>35446666</td>\n", "    </tr>\n", "    <tr>\n", "      <th>550</th>\n", "      <td>191</td>\n", "      <td>10</td>\n", "      <td>COULIBALY EPSE DIAKITE DOHOPIERI SIRAMANE MARIAM</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>95</td>\n", "      <td>67920000</td>\n", "      <td>26000000</td>\n", "      <td>41920000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>551</th>\n", "      <td>192</td>\n", "      <td>10</td>\n", "      <td>FLEUR DE LYS SCI</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>74</td>\n", "      <td>67500000</td>\n", "      <td>35973333</td>\n", "      <td>31526667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>552</th>\n", "      <td>193</td>\n", "      <td>10</td>\n", "      <td>FLEUR DE LYS SCI</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>68</td>\n", "      <td>67500000</td>\n", "      <td>35973333</td>\n", "      <td>31526667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>553</th>\n", "      <td>194</td>\n", "      <td>10</td>\n", "      <td>SCI AURORE BOREALE</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>72</td>\n", "      <td>67500000</td>\n", "      <td>24000000</td>\n", "      <td>43500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>554</th>\n", "      <td>195</td>\n", "      <td>10</td>\n", "      <td>SCI AURORE BOREALE</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>68</td>\n", "      <td>67500000</td>\n", "      <td>24000000</td>\n", "      <td>43500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>555</th>\n", "      <td>196</td>\n", "      <td>10</td>\n", "      <td>SCI SAUYET</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>69</td>\n", "      <td>67500000</td>\n", "      <td>29000000</td>\n", "      <td>38500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>556</th>\n", "      <td>198</td>\n", "      <td>10</td>\n", "      <td>VALLET SAMAN ANNE FRANCOISE</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>65</td>\n", "      <td>67500000</td>\n", "      <td>27250000</td>\n", "      <td>40250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>557</th>\n", "      <td>199</td>\n", "      <td>10</td>\n", "      <td>TOURE KOANIMAN</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>64</td>\n", "      <td>67500000</td>\n", "      <td>32500000</td>\n", "      <td>35000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>558</th>\n", "      <td>200</td>\n", "      <td>10</td>\n", "      <td>ZOZO-BOLI LAURENT &amp; PATRICIA ANNICK</td>\n", "      <td>DUPLEX 4 PIÈCES</td>\n", "      <td>64</td>\n", "      <td>67500000</td>\n", "      <td>25999900</td>\n", "      <td>41500100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     lot ilot                                            client  \\\n", "530  202   12             KOUKOU GBALEY KRAGBE ACHILLE NARCISSE   \n", "531  203   12             KOUKOU GBALEY KRAGBE ACHILLE NARCISSE   \n", "532  204   12                  MERESSO YAO AMON CYNTHIA EVELYNE   \n", "533  169   10                    AZAGOH KOUASSI KOUAKOU GERMAIN   \n", "534  229   18           TANOE AMON MARIE-CHRISTELLE CHINMINGNON   \n", "535  246   20        ISIAKA ET SANOGO ABDOU JOKOOLA ET MATEININ   \n", "536  264   20                NANA CEDRIC SEBASTIEN NYAKAM NGOKO   \n", "537  288   22                                         KONE SITA   \n", "538  289   22                       OUATTARA BIAKEREBE AICHATOU   \n", "539  290   22                           COULIBALY MANOUM ISSOUF   \n", "540  181   10                                     SCI NOLETHIKA   \n", "541  182   10                        JACQUES ARSENE TEGNAN EBBA   \n", "542  183   10                        GNAGNE-ADOU MARIE-MARCELLE   \n", "543  184   10                       VALLET SAMAN ANNE FRANCOISE   \n", "544  185   10                            KOUAKOU OFORI FRANÇOIS   \n", "545  186   10                     N'GUETTIA YAO JUNIOR STEPHANE   \n", "546  187   10                                     SCI NOLETHIKA   \n", "547  188   10                                     SCI NOLETHIKA   \n", "548  189   10                                     SCI NOLETHIKA   \n", "549  190   10                                  FLEUR DE LYS SCI   \n", "550  191   10  COULIBALY EPSE DIAKITE DOHOPIERI SIRAMANE MARIAM   \n", "551  192   10                                  FLEUR DE LYS SCI   \n", "552  193   10                                  FLEUR DE LYS SCI   \n", "553  194   10                               SCI AURORE BOREALE    \n", "554  195   10                               SCI AURORE BOREALE    \n", "555  196   10                                        SCI SAUYET   \n", "556  198   10                       VALLET SAMAN ANNE FRANCOISE   \n", "557  199   10                                    TOURE KOANIMAN   \n", "558  200   10               ZOZO-BOLI LAURENT & PATRICIA ANNICK   \n", "\n", "              type_actif avancement_travaux_mois cout_actif  paiement_en_date  \\\n", "530  DUPLEX 3 PIÈCES MIT                      50   42000000          16850000   \n", "531  DUPLEX 3 PIÈCES MIT                      47   42000000          15850100   \n", "532  DUPLEX 3 PIÈCES MIT                      50   42000000           3500000   \n", "533  DUPLEX 4 PIÈCES MIT                      68   60360000          10272000   \n", "534  DUPLEX 4 PIÈCES MIT                      59   57000000           9222000   \n", "535  DUPLEX 4 PIÈCES MIT                      55   57000000          37000000   \n", "536  DUPLEX 4 PIÈCES MIT                      63   57000000          39000000   \n", "537  DUPLEX 4 PIÈCES MIT                      44   59240000          45500000   \n", "538  DUPLEX 4 PIÈCES MIT                      40   59030000           5500000   \n", "539  DUPLEX 4 PIÈCES MIT                      39   60710000          30000000   \n", "540      DUPLEX 4 PIÈCES                      74   74500000          15000000   \n", "541      DUPLEX 4 PIÈCES                      74   67500000          17000000   \n", "542      DUPLEX 4 PIÈCES                      74   67500000          14000000   \n", "543      DUPLEX 4 PIÈCES                      74   67500000          27250000   \n", "544      DUPLEX 4 PIÈCES                      73   58387500           8000000   \n", "545      DUPLEX 4 PIÈCES                      74   67500000          26500000   \n", "546      DUPLEX 4 PIÈCES                      74   67500000          15000000   \n", "547      DUPLEX 4 PIÈCES                      74   67500000          15000000   \n", "548      DUPLEX 4 PIÈCES                      74   67500000          15000000   \n", "549      DUPLEX 4 PIÈCES                      20   71420000          35973334   \n", "550      DUPLEX 4 PIÈCES                      95   67920000          26000000   \n", "551      DUPLEX 4 PIÈCES                      74   67500000          35973333   \n", "552      DUPLEX 4 PIÈCES                      68   67500000          35973333   \n", "553      DUPLEX 4 PIÈCES                      72   67500000          24000000   \n", "554      DUPLEX 4 PIÈCES                      68   67500000          24000000   \n", "555      DUPLEX 4 PIÈCES                      69   67500000          29000000   \n", "556      DUPLEX 4 PIÈCES                      65   67500000          27250000   \n", "557      DUPLEX 4 PIÈCES                      64   67500000          32500000   \n", "558      DUPLEX 4 PIÈCES                      64   67500000          25999900   \n", "\n", "     reste_a_payer  \n", "530       25150000  \n", "531       26149900  \n", "532       38500000  \n", "533       50088000  \n", "534       47778000  \n", "535       20000000  \n", "536       18000000  \n", "537       13740000  \n", "538       53530000  \n", "539       30710000  \n", "540       59500000  \n", "541       50500000  \n", "542       53500000  \n", "543       40250000  \n", "544       50387500  \n", "545       41000000  \n", "546       52500000  \n", "547       52500000  \n", "548       52500000  \n", "549       35446666  \n", "550       41920000  \n", "551       31526667  \n", "552       31526667  \n", "553       43500000  \n", "554       43500000  \n", "555       38500000  \n", "556       40250000  \n", "557       35000000  \n", "558       41500100  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Affichage détaillé des lots par programme\n", "print(\"=== DÉTAIL DES LOTS PAR PROGRAMME ===\")\n", "\n", "for programme in df_lots['programme'].unique():\n", "    print(f\"\\n--- Programme: {programme} ---\")\n", "    lots_programme = df_lots[df_lots['programme'] == programme]\n", "    print(f\"Nombre de lots: {len(lots_programme)}\")\n", "    display(lots_programme[['lot', 'ilot', 'client', 'type_actif', 'avancement_travaux_mois', \n", "                           'cout_actif', 'paiement_en_date', 'reste_a_payer']])"]}, {"cell_type": "code", "execution_count": null, "id": "7b8585e7-e56f-469f-8c2b-884466363a69", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}