from django.db import models
from accounts.models import CustomUser


# Create your models here.
class UserProfile(models.Model):
    user = models.OneToOneField(
        CustomUser, on_delete=models.CASCADE, related_name="personal_information"
    )
    profile_picture = models.ImageField(
        upload_to="profile_pictures/", null=True, blank=True
    )
    country = models.CharField(max_length=100, blank=True, null=True)
    address = models.TextField(blank=True, null=True)


# DQE Models
# ========================================================


class DQEData(models.Model):
    """
    Model to store DQE data fetched from external API
    """

    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"DQE Data - {self.title or 'Untitled'} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"


# ================
# G-Stock
# ================

# A. Approvisionnement de stock


class GStockApprovisionnement(models.Model):
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"G-Stock Approvisionnement - {self.title or 'Untitled'} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"


# B. Sortie de stock


class GStockSortie(models.Model):
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"G-Stock Sortie - {self.title or 'Untitled'} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"


# C. Etat du niveau de consommation des stocks


class GStockConsommation(models.Model):
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"G-Stock Consommation - {self.title or 'Untitled'} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"


# D. Achat de matériaux


class GStockAchat(models.Model):
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"G-Stock Achat - {self.title or 'Untitled'} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"


# =================
# G-Projet
# ===============


class GProjet(models.Model):
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


# =================
# Ecole des talents
# =================


class EcoleTalents(models.Model):
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

# =================
# G_locative
# =================

class GLocative(models.Model):
    """
    Model to store GLocative data fetched from external API
    """

    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"DQE Data - {self.title or 'Untitled'} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"


# =================
# E_syndic
# =================

class ESyndic(models.Model):
    """
    Model to store ESyndic data fetched from external API
    """

    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"ESyndic Data - {self.title or 'Untitled'} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"


# =================
# Sentinelle
# =================

class Sentinelle(models.Model):
    """
    Model to store Sentinelle data fetched from external API
    """

    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Sentinelle Data - {self.title or 'Untitled'} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"

# ===================
# CRM
# ===================

class CRM(models.Model):
    """
    Model to store CRM data fetched from external API
    """

    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"CRM Data - {self.title or 'Untitled'} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"


# =================
# Donnees macroeconomiques - PIB
# =================


class DonneesProduitInterieurBrut(models.Model):
    annee_deb_couv = models.IntegerField(default=2024)
    mois_deb_couv = models.IntegerField(default=1)
    annee_fin_couv = models.IntegerField(default=2024)
    mois_fin_couv = models.IntegerField(default=12)
    valeur = models.FloatField(default=0.0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Donnees PIB - {self.annee_deb_couv} : {self.valeur}"


# =================
# Donnees macroeconomiques - PIB
# =================


class donneesInflation(models.Model):
    country_name = models.CharField(max_length=255, blank=True, null=True)
    country_code = models.CharField(max_length=10, blank=True, null=True)
    indicator_name = models.CharField(max_length=255, blank=True, null=True)
    indicator_code = models.CharField(max_length=50, blank=True, null=True)
    year = models.IntegerField()
    value = models.FloatField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Donnees Inflation - {self.country_name} ({self.year}) : {self.value}"


# =================
# Donnees macroeconomiques - Taux Pret Immobilier
# =================

class DonnesTauxPretImmobilier(models.Model):
    banque = models.CharField(max_length=255)
    type_pret = models.CharField(max_length=100)
    taux_interet = models.CharField(max_length=50)
    duree_pret = models.CharField(max_length=50)    
    frais_dossier = models.TextField()
    conditions = models.TextField(blank=True, null=True)
    source = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Donnees Taux Pret Immobilier - {self.banque} ({self.type_pret}) : {self.taux_interet}"


# =================
# Donnees du marche immobilier - Prix au m2
# =================

class DonneesPrixMetreCarre(models.Model):
    commune = models.CharField(max_length=255)  
    prix_min = models.IntegerField()
    prix_max = models.IntegerField()
    prix_moyen = models.IntegerField()
    source = models.URLField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Donnees Prix au m2 - {self.commune} : {self.prix_moyen}"


# =================
# Donnees du marche immobilier - Materiaux de construction
# =================

class DonneesMateriauxConstruction(models.Model):
    categorie = models.CharField(max_length=255)
    titre = models.CharField(max_length=255)
    prix = models.CharField(max_length=100)
    description = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Donnees Materiaux de construction - {self.titre} ({self.categorie}) : {self.prix}"


# =================
# Donnees demographique et sociales - Porjection demographique 
# =================

class DonneesProjectionDemographique(models.Model):
    region = models.CharField(max_length=225)
    annee = models.IntegerField()
    population_totale = models.IntegerField()
    population_urbaine = models.IntegerField()
    population_rurale = models.IntegerField()
    taux_croissance = models.FloatField()
    densite = models.FloatField()
    source = models.URLField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Donnees Projection Demographique - {self.region} ({self.annee}) : {self.population_totale}"


# =================
# Donnees demographique et sociales - Porjection demographique 
# =================

class DonneesMigrationInterne(models.Model):
    annee_reference = models.IntegerField()
    region_origine = models.CharField(max_length=255)
    region_destination = models.CharField(max_length=255)
    nombre_migrants_total = models.IntegerField()
    source_donnee = models.CharField(max_length=500)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Migration {self.region_origine} -> {self.region_destination} ({self.annee_reference}) : {self.nombre_migrants_total} migrants"