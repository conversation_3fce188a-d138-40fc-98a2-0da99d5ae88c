"""
API Views pour Kaydan Analytics Hub
Gestion des endpoints REST pour tous les modèles de données
Version propre et organisée
"""

# ========================================
# IMPORTS DJANGO ET DRF
# ========================================
from django.shortcuts import render, get_object_or_404
from django.db import models
from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.viewsets import ViewSet
from knox.auth import TokenAuthentication
import requests
from datetime import datetime
import base64
from django.http import HttpResponse
import json

# ========================================
# IMPORTS MODÈLES
# ========================================
from .models import (
    # Modèles utilisateur
    UserProfile,
    
    # Modèles de données externes existants
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
    EcoleTalents,
    GLocative,
    ESyndic,
    Sentinelle,
    CRM,
    
    # Nouveaux modèles de données externes
    DonneesProduitInterieurBrut,
    donneesInflation,
    DonnesTauxPretImmobilier,
    DonneesPrixMetreCarre,
    DonneesMateriauxConstruction,
    DonneesProjectionDemographique,
    DonneesMigrationInterne,
)

from accounts.models import CustomUser

# ========================================
# IMPORTS SERIALIZERS
# ========================================
from .serializers import (
    # Serializers utilisateur
    UserProfileSerializer,
    UserProfileUpdateSerializer,
    
    # Serializers données externes existants
    DQEDataSerializer,
    GStockApprovisionnementSerializer,
    GStockSortieSerializer,
    GStockConsommationSerializer,
    GStockAchatSerializer,
    GProjetSerializer,
    EcoleTalentSerializer,
    GLocativeSerializer,
    ESyndicSerializer,
    SentinelleSerializer,
    CRMSerializer,
    
    # Nouveaux serializers
    DonneesProduitInterieurBrutSerializer,
    DonneesInflationSerializer,
    DonnesTauxPretImmobilierSerializer,
    DonneesPrixMetreCarreSerializer,
    DonneesMateriauxConstructionSerializer,
    DonneesProjectionDemographiqueSerializer,
    DonneesMigrationInterneSerializer,
)


# ========================================
# VIEWSETS UTILISATEUR
# ========================================

class UserProfileViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des profils utilisateur
    """
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        """Filtrer par utilisateur connecté"""
        return UserProfile.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Associer le profil à l'utilisateur connecté"""
        serializer.save(user=self.request.user)

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Récupère le profil de l'utilisateur connecté"""
        try:
            profile = UserProfile.objects.get(user=request.user)
            serializer = self.get_serializer(profile)
            return Response(serializer.data)
        except UserProfile.DoesNotExist:
            return Response(
                {"error": "Profil non trouvé"}, 
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['put', 'patch'])
    def update_profile(self, request):
        """Met à jour le profil de l'utilisateur connecté"""
        try:
            profile = UserProfile.objects.get(user=request.user)
            serializer = UserProfileUpdateSerializer(
                profile, 
                data=request.data, 
                partial=request.method == 'PATCH'
            )
            if serializer.is_valid():
                serializer.save()
                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except UserProfile.DoesNotExist:
            return Response(
                {"error": "Profil non trouvé"}, 
                status=status.HTTP_404_NOT_FOUND
            )


# ========================================
# VIEWSETS DONNÉES EXTERNES EXISTANTES
# ========================================

class KreDQEData(ViewSet):
    """
    ViewSet pour les données DQE (Devis Quantitatif Estimatif)
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données DQE depuis l'API externe"""
        try:
            # URL de l'API externe DQE
            api_url = "http://*************:8000/api/dqes/getData-to-kah"
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Sauvegarder en base
                dqe_entry = DQEData.objects.create(
                    title="DQE Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )
                
                serializer = DQEDataSerializer(dqe_entry)
                return Response({
                    "status": "success",
                    "message": "Données DQE récupérées et sauvegardées",
                    "data": serializer.data
                })
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API externe: {response.status_code}"
                }, status=status.HTTP_502_BAD_GATEWAY)
                
        except requests.RequestException as e:
            return Response({
                "status": "error",
                "message": f"Erreur de connexion: {str(e)}"
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur interne: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données DQE de la base"""
        try:
            latest_data = DQEData.objects.latest("created_at")
            serializer = DQEDataSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except DQEData.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée DQE trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données DQE"""
        data = DQEData.objects.all().order_by('-created_at')
        serializer = DQEDataSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


# ========================================
# VIEWSETS G-STOCK (Gestion de Stock)
# ========================================

class GStockApprovisionnementViewSet(ViewSet):
    """
    ViewSet pour les données d'approvisionnement G-Stock
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère les données d'approvisionnement depuis G-Stock"""
        try:
            api_url = "https://gstock.artemisconstruction-ci.com/params/get-appro"
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Sauvegarder en base
                appro_entry = GStockApprovisionnement.objects.create(
                    title="G-Stock Approvisionnement",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )
                
                serializer = GStockApprovisionnementSerializer(appro_entry)
                return Response({
                    "status": "success",
                    "message": "Données d'approvisionnement récupérées",
                    "data": serializer.data
                })
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API G-Stock: {response.status_code}"
                }, status=status.HTTP_502_BAD_GATEWAY)
                
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données d'approvisionnement"""
        try:
            latest_data = GStockApprovisionnement.objects.latest("created_at")
            serializer = GStockApprovisionnementSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except GStockApprovisionnement.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée d'approvisionnement trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données d'approvisionnement"""
        data = GStockApprovisionnement.objects.all().order_by('-created_at')
        serializer = GStockApprovisionnementSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


# ========================================
# VIEWSETS ECOLE DES TALENTS
# ========================================

class EcoleTalentsViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données Ecole des Talents
    """

    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données Ecole des Talents depuis l'API externe"""
        try:
            # URL de l'API externe Ecole des Talents
            api_url = "https://talents.kaydangroupe.com/params/data-to-kah"
            response = requests.get(api_url, timeout=30)

            if response.status_code == 200:
                data = response.json()

                # Sauvegarder en base
                talents_entry = EcoleTalents.objects.create(
                    title="Ecole des Talents Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data,
                )

                serializer = EcoleTalentSerializer(talents_entry)
                return Response(
                    {
                        "status": "success",
                        "message": "Données Ecole des Talents récupérées et sauvegardées",
                        "data": serializer.data,
                    }
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": f"Erreur API externe: {response.status_code}",
                    },
                    status=status.HTTP_502_BAD_GATEWAY,
                )

        except requests.RequestException as e:
            return Response(
                {"status": "error", "message": f"Erreur de connexion: {str(e)}"},
                status=status.HTTP_503_SERVICE_UNAVAILABLE,
            )
        except Exception as e:
            return Response(
                {"status": "error", "message": f"Erreur interne: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @action(detail=False, methods=["get"])
    def get_latest(self, request):
        """Récupère les dernières données Ecole des Talents de la base"""
        try:
            latest_data = EcoleTalents.objects.latest("created_at")
            serializer = EcoleTalentSerializer(latest_data)
            return Response({"status": "success", "data": serializer.data})
        except EcoleTalents.DoesNotExist:
            return Response(
                {
                    "status": "error",
                    "message": "Aucune donnée Ecole des Talents trouvée",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

    @action(detail=False, methods=["get"])
    def list_all(self, request):
        """Liste toutes les données Ecole des Talents"""
        data = EcoleTalents.objects.all().order_by("-created_at")
        serializer = EcoleTalentSerializer(data, many=True)
        return Response(
            {"status": "success", "count": len(data), "data": serializer.data}
        )


# ========================================
# VIEWSETS G_LOCATIVE (Gestion de Locative)
# ========================================

class KreGLocativeData(ViewSet):
    """
    ViewSet pour les données G-Locative
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données G-Locative depuis l'API externe"""
        try:
            # URL de l'API externe G-Locative
            api_url = "https://locative.inoovim.com/api/listdata"
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Sauvegarder en base
                locative_entry = GLocative.objects.create(
                    title="GLocative Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )
                
                serializer = GLocativeSerializer(locative_entry)
                return Response({
                    "status": "success",
                    "message": "Données G-Locative récupérées et sauvegardées",
                    "data": serializer.data
                })
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API externe: {response.status_code}"
                }, status=status.HTTP_502_BAD_GATEWAY)
                
        except requests.RequestException as e:
            return Response({
                "status": "error",
                "message": f"Erreur de connexion: {str(e)}"
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur interne: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données G-Locative de la base"""
        try:
            latest_data = GLocative.objects.latest("created_at")
            serializer = GLocativeSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except GLocative.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée G-Locative trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données G-Locative"""
        data = GLocative.objects.all().order_by('-created_at')
        serializer = GLocativeSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })


# ========================================
# VIEWSETS E_SYNDIC (Gestion de Syndic)
# ========================================


class KreESyndicData(ViewSet):
    """
    ViewSet pour les données E-Syndic
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données E-Syndic depuis l'API externe"""
        try:
            # URL de l'API externe E-Syndic
            api_url = "https://e-syndic.inoovim.com/api/listdata"
            response = requests.get(api_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                # Sauvegarder en base
                syndic_entry = ESyndic.objects.create(
                    title="ESyndic Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )
                
                serializer = ESyndicSerializer(syndic_entry)
                return Response({
                    "status": "success",
                    "message": "Données E-Syndic récupérées et sauvegardées",
                    "data": serializer.data
                })
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API externe: {response.status_code}"
                }, status=status.HTTP_502_BAD_GATEWAY)
                
        except requests.RequestException as e:
            return Response({
                "status": "error",
                "message": f"Erreur de connexion: {str(e)}"
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur interne: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données E-Syndic de la base"""
        try:
            latest_data = ESyndic.objects.latest("created_at")
            serializer = ESyndicSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except ESyndic.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée E-Syndic trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données E-Syndic"""
        data = ESyndic.objects.all().order_by('-created_at')
        serializer = ESyndicSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

# ========================================
# VIEWSETS SENTINELLE
# ========================================


class KreSentinelleData(ViewSet):
    """
    ViewSet pour les données Sentinelle avec authentification Basic et API Key
    """
    permission_classes = [permissions.IsAuthenticated]

    def list(self, request):
        """Récupère et sauvegarde les données Sentinelle depuis l'API externe"""
        try:
            # URL de l'API externe Sentinelle
            api_url = "http://sentinelle.kaydanrealestate.com:8181/api/services/taux_programme"

            # Credentials pour l'authentification Basic Auth
            username = "admin"
            password = "D@t@rium@1545#"

            # Encoder les credentials en Base64 pour Basic Auth
            credentials = f"{username}:{password}"
            encoded_credentials = base64.b64encode(credentials.encode('utf-8')).decode('utf-8')

            headers = {
                'Authorization': f'Basic {encoded_credentials}',
                'X-API-KEY': '5632d76ece5711436d3084a628f2afb03388a373'
            }
            response = requests.get(api_url, headers=headers, timeout=30)

            if response.status_code == 200:
                data = response.json()

                # Sauvegarder en base
                sentinelle_entry = Sentinelle.objects.create(
                    title="Sentinelle Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )

                serializer = SentinelleSerializer(sentinelle_entry)
                return Response({
                    "status": "success",
                    "message": "Données Sentinelle récupérées et sauvegardées",
                    "data": serializer.data
                })
            elif response.status_code == 401:
                return Response({
                    "status": "error",
                    "message": "Erreur d'authentification - Vérifiez les credentials"
                }, status=status.HTTP_502_BAD_GATEWAY)
            elif response.status_code == 403:
                return Response({
                    "status": "error",
                    "message": "Accès refusé - Vérifiez l'API Key"
                }, status=status.HTTP_502_BAD_GATEWAY)
            else:
                return Response({
                    "status": "error",
                    "message": f"Erreur API externe: {response.status_code} - {response.text}"
                }, status=status.HTTP_502_BAD_GATEWAY)

        except requests.RequestException as e:
            return Response({
                "status": "error",
                "message": f"Erreur de connexion: {str(e)}"
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            return Response({
                "status": "error",
                "message": f"Erreur interne: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère les dernières données Sentinelle de la base"""
        try:
            latest_data = Sentinelle.objects.latest("created_at")
            serializer = SentinelleSerializer(latest_data)
            return Response({
                "status": "success",
                "data": serializer.data
            })
        except Sentinelle.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée Sentinelle trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données Sentinelle"""
        data = Sentinelle.objects.all().order_by('-created_at')
        serializer = SentinelleSerializer(data, many=True)
        return Response({
            "status": "success",
            "count": len(data),
            "data": serializer.data
        })

# ========================================
# VIEWSETS CRM
# ========================================

class KreCRMData(ViewSet):
    """
    ViewSet pour les données CRM
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def list(self, request):
        """Récupère et sauvegarde les données CRM depuis l'API externe"""
        try:
            # URL exactement comme dans Postman (avec Authorization en query param)
            api_url = "https://crm.inoovim.com/api/analytics/v1/hub?Authorization=Hub%20cf7e143b4eb16550f940f2e00f13e0118ebad663"
            
            # Headers minimaux
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            print(f"Début de l'appel API à {datetime.now()}: {api_url}")
            
            # Timeout adapté : l'API met ~44s à répondre dans Postman
            response = requests.get(
                api_url, 
                headers=headers, 
                timeout=(15, 60),  # 15s pour se connecter, 60s pour lire (marge de sécurité)
                stream=False
            )
            
            print(f"Réponse reçue à {datetime.now()} - Status: {response.status_code}")
            
            if response.status_code == 200:
                print("Parsing JSON...")
                data = response.json()
                data_size = len(str(data))
                print(f"Données reçues: {data_size} caractères")
                
                # Sauvegarder en base
                locative_entry = CRM.objects.create(
                    title="CRM Data Import",
                    description=f"Import automatique du {datetime.now()}",
                    data=data
                )
                
                # SOLUTION 1: Retourner seulement un résumé au lieu des données complètes
                # Analyser la structure des données pour donner un aperçu
                summary = self._generate_data_summary(data)
                
                return Response({
                    "status": "success",
                    "message": f"Données CRM récupérées et sauvegardées",
                    "import_info": {
                        "id": locative_entry.id,
                        "created_at": locative_entry.created_at,
                        "data_size_chars": data_size,
                        "data_size_mb": round(data_size / 1024 / 1024, 2)
                    },
                    "data_summary": summary,
                    "note": "Données complètes sauvegardées en base. Utilisez /api/crm/get_latest/ pour récupérer un extrait."
                })
            else:
                print(f"Erreur API: {response.status_code} - {response.text[:200]}")
                return Response({
                    "status": "error",
                    "message": f"Erreur API externe: {response.status_code} - {response.text}"
                }, status=status.HTTP_502_BAD_GATEWAY)
                
        except requests.exceptions.Timeout as e:
            print(f"Timeout après 60s: {str(e)}")
            return Response({
                "status": "error",
                "message": "L'API CRM met trop de temps à répondre (>60s). Réessayez plus tard."
            }, status=status.HTTP_504_GATEWAY_TIMEOUT)
        except requests.RequestException as e:
            print(f"Erreur de connexion: {str(e)}")
            return Response({
                "status": "error",
                "message": f"Erreur de connexion: {str(e)}"
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
        except Exception as e:
            print(f"Erreur interne: {str(e)}")
            return Response({
                "status": "error",
                "message": f"Erreur interne: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _generate_data_summary(self, data):
        """Génère un résumé des données pour éviter d'envoyer 10MB au navigateur"""
        try:
            if isinstance(data, dict):
                summary = {
                    "type": "object",
                    "keys_count": len(data.keys()),
                    "keys": list(data.keys())[:10],  # Seulement les 10 premières clés
                }
                if len(data.keys()) > 10:
                    summary["keys"].append(f"... et {len(data.keys()) - 10} autres clés")
                
                # Analyser quelques valeurs pour donner une idée
                for key in list(data.keys())[:3]:
                    value = data[key]
                    if isinstance(value, list):
                        summary[f"{key}_info"] = {
                            "type": "array",
                            "length": len(value),
                            "sample": value[:2] if len(value) > 0 else []
                        }
                    elif isinstance(value, dict):
                        summary[f"{key}_info"] = {
                            "type": "object",
                            "keys": list(value.keys())[:5]
                        }
                    else:
                        summary[f"{key}_info"] = {
                            "type": type(value).__name__,
                            "value": str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                        }
                
            elif isinstance(data, list):
                summary = {
                    "type": "array",
                    "length": len(data),
                    "sample_items": data[:3] if len(data) > 0 else []
                }
            else:
                summary = {
                    "type": type(data).__name__,
                    "preview": str(data)[:200] + "..." if len(str(data)) > 200 else str(data)
                }
            
            return summary
        except Exception as e:
            return {"error": f"Impossible de générer le résumé: {str(e)}"}

    @action(detail=False, methods=['get'])
    def get_latest(self, request):
        """Récupère un extrait des dernières données CRM de la base"""
        try:
            latest_data = CRM.objects.latest("created_at")
            
            # Option 1: Retourner seulement les métadonnées
            basic_info = {
                "id": latest_data.id,
                "title": latest_data.title,
                "description": latest_data.description,
                "created_at": latest_data.created_at,
                "data_size": len(str(latest_data.data)),
            }
            
            # Option 2: Retourner un extrait des données (paramètre ?full=true pour tout)
            include_full = request.query_params.get('full', 'false').lower() == 'true'
            
            if include_full:
                # ATTENTION: Cela peut planter le navigateur !
                serializer = CRMSerializer(latest_data)
                return Response({
                    "status": "success",
                    "warning": "Données complètes - peut être lourd pour le navigateur",
                    "data": serializer.data
                })
            else:
                # Retourner seulement un résumé sécurisé
                summary = self._generate_data_summary(latest_data.data)
                return Response({
                    "status": "success",
                    "data": {
                        **basic_info,
                        "data_summary": summary
                    },
                    "note": "Ajoutez ?full=true pour voir toutes les données (attention: très lourd)"
                })
                
        except CRM.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée CRM trouvée"
            }, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=False, methods=['get'])
    def list_all(self, request):
        """Liste toutes les données CRM (métadonnées seulement)"""
        crm_objects = CRM.objects.all().order_by('-created_at')
        
        # Retourner seulement les métadonnées, pas les données JSON complètes
        lightweight_data = []
        for obj in crm_objects:
            lightweight_data.append({
                "id": obj.id,
                "title": obj.title,
                "description": obj.description,
                "created_at": obj.created_at,
                "data_size_chars": len(str(obj.data)),
                "data_size_mb": round(len(str(obj.data)) / 1024 / 1024, 2)
            })
        
        return Response({
            "status": "success",
            "count": len(lightweight_data),
            "data": lightweight_data,
            "note": "Métadonnées seulement. Utilisez /api/crm/get_latest/ pour voir le contenu."
        })

    @action(detail=False, methods=['get'])
    def download_latest(self, request):
        """Télécharge les dernières données CRM en tant que fichier JSON"""
        try:
            latest_data = CRM.objects.latest("created_at")
            
            # Créer une réponse de téléchargement de fichier
            response = HttpResponse(
                json.dumps(latest_data.data, indent=2, ensure_ascii=False),
                content_type='application/json'
            )
            response['Content-Disposition'] = f'attachment; filename="crm_data_{latest_data.created_at.strftime("%Y%m%d_%H%M%S")}.json"'
            
            return response
            
        except CRM.DoesNotExist:
            return Response({
                "status": "error",
                "message": "Aucune donnée CRM trouvée"
            }, status=status.HTTP_404_NOT_FOUND)

# ========================================
# VIEWSETS NOUVEAUX MODÈLES DE DONNÉES
# ========================================

class DonneesProduitInterieurBrutViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données PIB (Produit Intérieur Brut)
    Fournit les opérations CRUD complètes : GET, POST, PUT, PATCH, DELETE
    """
    queryset = DonneesProduitInterieurBrut.objects.all()
    serializer_class = DonneesProduitInterieurBrutSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def latest(self, request):
        """Récupère la dernière donnée PIB"""
        try:
            latest = self.queryset.latest('created_at')
            serializer = self.get_serializer(latest)
            return Response(serializer.data)
        except DonneesProduitInterieurBrut.DoesNotExist:
            return Response(
                {"error": "Aucune donnée PIB trouvée"},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['get'])
    def by_year(self, request):
        """Filtre les données PIB par année"""
        annee = request.query_params.get('annee')
        if not annee:
            return Response(
                {"error": "Le paramètre 'annee' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(annee_deb_couv=annee)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)


class DonneesInflationViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données d'inflation
    """
    queryset = donneesInflation.objects.all()
    serializer_class = DonneesInflationSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_country(self, request):
        """Filtre les données d'inflation par pays"""
        country_code = request.query_params.get('country_code')
        if not country_code:
            return Response(
                {"error": "Le paramètre 'country_code' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(country_code=country_code)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def latest_by_country(self, request):
        """Récupère la dernière donnée d'inflation par pays"""
        country_code = request.query_params.get('country_code')
        if not country_code:
            return Response(
                {"error": "Le paramètre 'country_code' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            latest = self.queryset.filter(country_code=country_code).latest('year')
            serializer = self.get_serializer(latest)
            return Response(serializer.data)
        except donneesInflation.DoesNotExist:
            return Response(
                {"error": f"Aucune donnée d'inflation trouvée pour {country_code}"},
                status=status.HTTP_404_NOT_FOUND
            )


class DonnesTauxPretImmobilierViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des taux de prêt immobilier
    """
    queryset = DonnesTauxPretImmobilier.objects.all()
    serializer_class = DonnesTauxPretImmobilierSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_bank(self, request):
        """Filtre les taux par banque"""
        banque = request.query_params.get('banque')
        if not banque:
            return Response(
                {"error": "Le paramètre 'banque' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(banque__icontains=banque)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_loan_type(self, request):
        """Filtre les taux par type de prêt"""
        type_pret = request.query_params.get('type_pret')
        if not type_pret:
            return Response(
                {"error": "Le paramètre 'type_pret' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(type_pret__icontains=type_pret)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)


class DonneesPrixMetreCarreViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des prix au mètre carré
    """
    queryset = DonneesPrixMetreCarre.objects.all()
    serializer_class = DonneesPrixMetreCarreSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_commune(self, request):
        """Filtre les prix par commune"""
        commune = request.query_params.get('commune')
        if not commune:
            return Response(
                {"error": "Le paramètre 'commune' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(commune__icontains=commune)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def price_range(self, request):
        """Filtre les prix par plage de prix"""
        prix_min = request.query_params.get('prix_min')
        prix_max = request.query_params.get('prix_max')

        if not prix_min or not prix_max:
            return Response(
                {"error": "Les paramètres 'prix_min' et 'prix_max' sont requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            prix_min = float(prix_min)
            prix_max = float(prix_max)
        except ValueError:
            return Response(
                {"error": "Les prix doivent être des nombres valides"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(
            prix_moyen__gte=prix_min,
            prix_moyen__lte=prix_max
        )
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)


class DonneesMateriauxConstructionViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des matériaux de construction
    """
    queryset = DonneesMateriauxConstruction.objects.all()
    serializer_class = DonneesMateriauxConstructionSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_category(self, request):
        """Filtre les matériaux par catégorie"""
        categorie = request.query_params.get('categorie')
        if not categorie:
            return Response(
                {"error": "Le paramètre 'categorie' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(categorie__icontains=categorie)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Recherche dans les matériaux par titre ou description"""
        q = request.query_params.get('q')
        if not q:
            return Response(
                {"error": "Le paramètre de recherche 'q' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(
            models.Q(titre__icontains=q) |
            models.Q(description__icontains=q)
        )
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)


class DonneesProjectionDemographiqueViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des projections démographiques
    """
    queryset = DonneesProjectionDemographique.objects.all()
    serializer_class = DonneesProjectionDemographiqueSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_region(self, request):
        """Filtre les projections par région"""
        region = request.query_params.get('region')
        if not region:
            return Response(
                {"error": "Le paramètre 'region' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(region__icontains=region)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def latest_by_region(self, request):
        """Récupère la dernière projection par région"""
        region = request.query_params.get('region')
        if not region:
            return Response(
                {"error": "Le paramètre 'region' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            latest = self.queryset.filter(region__icontains=region).latest('annee')
            serializer = self.get_serializer(latest)
            return Response(serializer.data)
        except DonneesProjectionDemographique.DoesNotExist:
            return Response(
                {"error": f"Aucune projection trouvée pour la région {region}"},
                status=status.HTTP_404_NOT_FOUND
            )


class DonneesMigrationInterneViewSet(viewsets.ModelViewSet):
    """
    ViewSet pour la gestion des données de migration interne
    """
    queryset = DonneesMigrationInterne.objects.all()
    serializer_class = DonneesMigrationInterneSerializer
    permission_classes = [permissions.IsAuthenticated]
    authentication_classes = [TokenAuthentication]

    @action(detail=False, methods=['get'])
    def by_origin(self, request):
        """Filtre les migrations par région d'origine"""
        region_origine = request.query_params.get('region_origine')
        if not region_origine:
            return Response(
                {"error": "Le paramètre 'region_origine' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(region_origine__icontains=region_origine)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_destination(self, request):
        """Filtre les migrations par région de destination"""
        region_destination = request.query_params.get('region_destination')
        if not region_destination:
            return Response(
                {"error": "Le paramètre 'region_destination' est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = self.queryset.filter(region_destination__icontains=region_destination)
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def top_migrations(self, request):
        """Récupère les top migrations par nombre de migrants"""
        limit = request.query_params.get('limit', 10)
        try:
            limit = int(limit)
        except ValueError:
            limit = 10

        data = self.queryset.order_by('-nombre_migrants_total')[:limit]
        serializer = self.get_serializer(data, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def migration_flows(self, request):
        """Analyse des flux migratoires par région"""
        # Agrégation des données par région d'origine et destination
        from django.db.models import Sum

        flows = self.queryset.values(
            'region_origine',
            'region_destination'
        ).annotate(
            total_migrants=Sum('nombre_migrants_total')
        ).order_by('-total_migrants')

        return Response({
            "status": "success",
            "count": len(flows),
            "data": list(flows)
        })
