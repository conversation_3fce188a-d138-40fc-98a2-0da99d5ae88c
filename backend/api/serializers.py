from rest_framework import serializers
from .models import (
    UserProfile,
    DQEData,
    GStockApprovisionnement,
    GStockSortie,
    GStockConsommation,
    GStockAchat,
    GProjet,
    EcoleTalents,
    GLocative,
    ESyndic,
    Sentinelle,
    CRM,
    DonneesProduitInterieurBrut,
    donneesInflation,
    DonnesTauxPretImmobilier,
    DonneesPrixMetreCarre,
    DonneesMateriauxConstruction,
    DonneesProjectionDemographique,
    DonneesMigrationInterne,
)
from accounts.serializers import UserSerializer


class UserProfileSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    profile_picture = serializers.SerializerMethodField()

    class Meta:
        model = UserProfile
        fields = "__all__"
        read_only_fields = ["id", "user"]

    def get_profile_picture(self, obj):
        if obj.profile_picture:
            request = self.context.get("request")
            if request:
                return request.build_absolute_uri(obj.profile_picture.url)
            return obj.profile_picture.url
        return None


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = [
            "profile_picture",
            "address",
        ]


class DQEDataSerializer(serializers.ModelSerializer):
    """
    Serializer for the DQEData model
    """

    class Meta:
        model = DQEData
        fields = ["id", "title", "description", "data", "created_at", "updated_at"]
        read_only_fields = ["id", "created_at", "updated_at"]


class GStockApprovisionnementSerializer(serializers.ModelSerializer):
    class Meta:
        model = GStockApprovisionnement
        fields = "__all__"


class GStockSortieSerializer(serializers.ModelSerializer):
    class Meta:
        model = GStockSortie
        fields = "__all__"


class GStockConsommationSerializer(serializers.ModelSerializer):
    class Meta:
        model = GStockConsommation
        fields = "__all__"


class GStockAchatSerializer(serializers.ModelSerializer):
    class Meta:
        model = GStockAchat
        fields = "__all__"


class GStockAchatSerializer(serializers.ModelSerializer):
    class Meta:
        model = GStockAchat
        fields = "__all__"


class GProjetSerializer(serializers.ModelSerializer):
    class Meta:
        model = GProjet
        fields = "__all__"


class EcoleTalentSerializer(serializers.ModelSerializer):
    class Meta:
        model = EcoleTalents
        fields = "__all__"


class GLocativeSerializer(serializers.ModelSerializer):
    class Meta:
        model = GLocative
        fields = "__all__"


class ESyndicSerializer(serializers.ModelSerializer):
    class Meta:
        model = ESyndic
        fields = "__all__"


class SentinelleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Sentinelle
        fields = "__all__"

class CRMSerializer(serializers.ModelSerializer):
    class Meta:
        model = CRM
        fields = "__all__"

class DonneesProduitInterieurBrutSerializer(serializers.ModelSerializer):
    """
    Serializer for PIB (Produit Intérieur Brut) data
    """

    class Meta:
        model = DonneesProduitInterieurBrut
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")

    def validate_annee_deb_couv(self, value):
        """Validation pour l'année de début"""
        if value < 1900 or value > 2100:
            raise serializers.ValidationError("L'année doit être entre 1900 et 2100")
        return value

    def validate_mois_deb_couv(self, value):
        """Validation pour le mois de début"""
        if value < 1 or value > 12:
            raise serializers.ValidationError("Le mois doit être entre 1 et 12")
        return value

    def validate_mois_fin_couv(self, value):
        """Validation pour le mois de fin"""
        if value < 1 or value > 12:
            raise serializers.ValidationError("Le mois doit être entre 1 et 12")
        return value


class DonneesInflationSerializer(serializers.ModelSerializer):
    """
    Serializer for Inflation data
    """

    class Meta:
        model = donneesInflation
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")

    def validate_year(self, value):
        """Validation pour l'année"""
        if value < 1900 or value > 2100:
            raise serializers.ValidationError("L'année doit être entre 1900 et 2100")
        return value


class DonnesTauxPretImmobilierSerializer(serializers.ModelSerializer):
    """
    Serializer for Taux de Prêt Immobilier data
    """

    class Meta:
        model = DonnesTauxPretImmobilier
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")

    def validate_banque(self, value):
        """Validation pour le nom de la banque"""
        if len(value.strip()) < 2:
            raise serializers.ValidationError("Le nom de la banque doit contenir au moins 2 caractères")
        return value.strip()


class DonneesPrixMetreCarreSerializer(serializers.ModelSerializer):
    """
    Serializer for Prix au Mètre Carré data
    """

    class Meta:
        model = DonneesPrixMetreCarre
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")

    def validate_prix_min(self, value):
        """Validation pour le prix minimum"""
        if value < 0:
            raise serializers.ValidationError("Le prix minimum ne peut pas être négatif")
        return value

    def validate_prix_max(self, value):
        """Validation pour le prix maximum"""
        if value < 0:
            raise serializers.ValidationError("Le prix maximum ne peut pas être négatif")
        return value

    def validate(self, data):
        """Validation croisée des prix"""
        if data.get('prix_min') and data.get('prix_max'):
            if data['prix_min'] > data['prix_max']:
                raise serializers.ValidationError("Le prix minimum ne peut pas être supérieur au prix maximum")
        return data


class DonneesMateriauxConstructionSerializer(serializers.ModelSerializer):
    """
    Serializer for Matériaux de Construction data
    """

    class Meta:
        model = DonneesMateriauxConstruction
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")

    def validate_titre(self, value):
        """Validation pour le titre"""
        if len(value.strip()) < 2:
            raise serializers.ValidationError("Le titre doit contenir au moins 2 caractères")
        return value.strip()


class DonneesProjectionDemographiqueSerializer(serializers.ModelSerializer):
    """
    Serializer for Projection Démographique data
    """

    class Meta:
        model = DonneesProjectionDemographique
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")

    def validate_annee(self, value):
        """Validation pour l'année"""
        if value < 1900 or value > 2100:
            raise serializers.ValidationError("L'année doit être entre 1900 et 2100")
        return value

    def validate_population_totale(self, value):
        """Validation pour la population totale"""
        if value < 0:
            raise serializers.ValidationError("La population ne peut pas être négative")
        return value

    def validate(self, data):
        """Validation croisée des populations"""
        pop_urbaine = data.get('population_urbaine', 0)
        pop_rurale = data.get('population_rurale', 0)
        pop_totale = data.get('population_totale', 0)

        if pop_urbaine and pop_rurale and pop_totale:
            if (pop_urbaine + pop_rurale) != pop_totale:
                raise serializers.ValidationError(
                    "La somme de la population urbaine et rurale doit égaler la population totale"
                )
        return data


class DonneesMigrationInterneSerializer(serializers.ModelSerializer):
    """
    Serializer for Migration Interne data
    """

    class Meta:
        model = DonneesMigrationInterne
        fields = "__all__"
        read_only_fields = ("id", "created_at", "updated_at")

    def validate_annee_reference(self, value):
        """Validation pour l'année de référence"""
        if value < 1900 or value > 2100:
            raise serializers.ValidationError("L'année doit être entre 1900 et 2100")
        return value

    def validate_nombre_migrants_total(self, value):
        """Validation pour le nombre de migrants"""
        if value < 0:
            raise serializers.ValidationError("Le nombre de migrants ne peut pas être négatif")
        return value
